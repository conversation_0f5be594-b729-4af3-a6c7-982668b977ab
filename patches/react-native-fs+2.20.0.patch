diff --git a/node_modules/react-native-fs/android/build.gradle b/node_modules/react-native-fs/android/build.gradle
index ddef857..41658f2 100644
--- a/node_modules/react-native-fs/android/build.gradle
+++ b/node_modules/react-native-fs/android/build.gradle
@@ -17,6 +17,7 @@ apply plugin: 'com.android.library'
 android {
     compileSdkVersion safeExtGet('compileSdkVersion', 26)
     buildToolsVersion safeExtGet('buildToolsVersion', '26.0.3')
+    namespace "com.rnfs"
     
     defaultConfig {
         minSdkVersion safeExtGet('minSdkVersion', 19)
diff --git a/node_modules/react-native-fs/android/build/generated/source/buildConfig/debug/SpeakUp/BuildConfig.java b/node_modules/react-native-fs/android/build/generated/source/buildConfig/debug/SpeakUp/BuildConfig.java
new file mode 100644
index 0000000..6c73cc4
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/generated/source/buildConfig/debug/SpeakUp/BuildConfig.java
@@ -0,0 +1,10 @@
+/**
+ * Automatically generated file. DO NOT MODIFY
+ */
+package SpeakUp;
+
+public final class BuildConfig {
+  public static final boolean DEBUG = Boolean.parseBoolean("true");
+  public static final String LIBRARY_PACKAGE_NAME = "SpeakUp";
+  public static final String BUILD_TYPE = "debug";
+}
diff --git a/node_modules/react-native-fs/android/build/intermediates/aar_metadata/debug/aar-metadata.properties b/node_modules/react-native-fs/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
new file mode 100644
index 0000000..1211b1e
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/aar_metadata/debug/aar-metadata.properties
@@ -0,0 +1,6 @@
+aarFormatVersion=1.0
+aarMetadataVersion=1.0
+minCompileSdk=1
+minCompileSdkExtension=0
+minAndroidGradlePluginVersion=1.0.0
+coreLibraryDesugaringEnabled=false
diff --git a/node_modules/react-native-fs/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json b/node_modules/react-native-fs/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
new file mode 100644
index 0000000..9e26dfe
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/annotation_processor_list/debug/annotationProcessors.json
@@ -0,0 +1 @@
+{}
\ No newline at end of file
diff --git a/node_modules/react-native-fs/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties b/node_modules/react-native-fs/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
new file mode 100644
index 0000000..d3bfc50
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/incremental/debug/packageDebugResources/compile-file-map.properties
@@ -0,0 +1 @@
+#Sat Aug 10 21:52:00 GMT+03:30 2024
diff --git a/node_modules/react-native-fs/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml b/node_modules/react-native-fs/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
new file mode 100644
index 0000000..40b5d19
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/incremental/debug/packageDebugResources/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-fs/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml b/node_modules/react-native-fs/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
new file mode 100644
index 0000000..3d26153
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/incremental/mergeDebugJniLibFolders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\main\jniLibs"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\debug\jniLibs"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-fs/android/build/intermediates/incremental/mergeDebugShaders/merger.xml b/node_modules/react-native-fs/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
new file mode 100644
index 0000000..82f31e0
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/incremental/mergeDebugShaders/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\main\shaders"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\debug\shaders"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-fs/android/build/intermediates/incremental/packageDebugAssets/merger.xml b/node_modules/react-native-fs/android/build/intermediates/incremental/packageDebugAssets/merger.xml
new file mode 100644
index 0000000..1c92667
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/incremental/packageDebugAssets/merger.xml
@@ -0,0 +1,2 @@
+<?xml version="1.0" encoding="utf-8"?>
+<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Projects\speakup\react native\speakup-react-native2\node_modules\react-native-fs\android\build\intermediates\shader_assets\debug\out"/></dataSet></merger>
\ No newline at end of file
diff --git a/node_modules/react-native-fs/android/build/intermediates/local_only_symbol_list/debug/R-def.txt b/node_modules/react-native-fs/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
new file mode 100644
index 0000000..78ac5b8
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/local_only_symbol_list/debug/R-def.txt
@@ -0,0 +1,2 @@
+R_DEF: Internal format may change without notice
+local
diff --git a/node_modules/react-native-fs/android/build/intermediates/navigation_json/debug/navigation.json b/node_modules/react-native-fs/android/build/intermediates/navigation_json/debug/navigation.json
new file mode 100644
index 0000000..0637a08
--- /dev/null
+++ b/node_modules/react-native-fs/android/build/intermediates/navigation_json/debug/navigation.json
@@ -0,0 +1 @@
+[]
\ No newline at end of file
diff --git a/node_modules/react-native-fs/android/src/main/AndroidManifest.xml b/node_modules/react-native-fs/android/src/main/AndroidManifest.xml
index 6e54f65..7ce2281 100644
--- a/node_modules/react-native-fs/android/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-fs/android/src/main/AndroidManifest.xml
@@ -1,5 +1,4 @@
 <?xml version="1.0" encoding="utf-8"?>
-<manifest xmlns:android="http://schemas.android.com/apk/res/android"
-    package="com.rnfs" >
+<manifest xmlns:android="http://schemas.android.com/apk/res/android">
 <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
 </manifest>
