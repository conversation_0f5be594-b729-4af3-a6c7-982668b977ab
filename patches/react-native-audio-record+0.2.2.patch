diff --git a/node_modules/react-native-audio-record/android/build.gradle b/node_modules/react-native-audio-record/android/build.gradle
index de6cdf1..98a0d77 100644
--- a/node_modules/react-native-audio-record/android/build.gradle
+++ b/node_modules/react-native-audio-record/android/build.gradle
@@ -12,7 +12,7 @@ def safeExtGet(prop, fallback) {
 android {
     compileSdkVersion safeExtGet("compileSdkVersion", DEFAULT_COMPILE_SDK_VERSION)
     buildToolsVersion safeExtGet("buildToolsVersion", DEFAULT_BUILD_TOOLS_VERSION)
-
+    namespace "com.goodatlas.audiorecord"
     defaultConfig {
         minSdkVersion safeExtGet('minSdkVersion', DEFAULT_MIN_SDK_VERSION)
         targetSdkVersion safeExtGet('targetSdkVersion', DEFAULT_TARGET_SDK_VERSION)
diff --git a/node_modules/react-native-audio-record/android/src/main/AndroidManifest.xml b/node_modules/react-native-audio-record/android/src/main/AndroidManifest.xml
index 5a10b11..4d9a2f0 100644
--- a/node_modules/react-native-audio-record/android/src/main/AndroidManifest.xml
+++ b/node_modules/react-native-audio-record/android/src/main/AndroidManifest.xml
@@ -1,5 +1,4 @@
 
-<manifest xmlns:android="http://schemas.android.com/apk/res/android"
-          package="com.goodatlas.audiorecord">
+<manifest xmlns:android="http://schemas.android.com/apk/res/android">
     <uses-permission android:name="android.permission.RECORD_AUDIO"/>
 </manifest>
