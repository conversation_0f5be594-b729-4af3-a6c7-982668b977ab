const fs = require("fs");
const path = require("path");

function findPackages(dir) {
  const results = [];

  function readPackageJson(pkgPath, parent) {
    try {
      const data = JSON.parse(fs.readFileSync(pkgPath, "utf8"));
      const author =
        typeof data.author === "string"
          ? data.author
          : data.author?.name || "";

      if (author.toLowerCase().includes("qix")) {
        results.push({
          name: data.name,
          version: data.version,
          author,
          parent: parent || "root (direct dependency)",
        });
      }
    } catch (e) {
      // skip invalid json
    }
  }

  function walk(nodeModulesPath, parent) {
    if (!fs.existsSync(nodeModulesPath)) return;
    const packages = fs.readdirSync(nodeModulesPath);

    for (const pkg of packages) {
      if (pkg.startsWith(".")) continue;

      const pkgPath = path.join(nodeModulesPath, pkg);

      if (pkg.startsWith("@")) {
        walk(pkgPath, parent);
        continue;
      }

      const packageJsonPath = path.join(pkgPath, "package.json");
      if (fs.existsSync(packageJsonPath)) {
        readPackageJson(packageJsonPath, parent);

        // همون اسم رو به عنوان parent پاس بده
        const childParent = JSON.parse(
          fs.readFileSync(packageJsonPath, "utf8")
        ).name;

        walk(path.join(pkgPath, "node_modules"), childParent);
      }
    }
  }

  walk(path.join(dir, "node_modules"), null);
  return results;
}

const found = findPackages(process.cwd());
console.log("Packages authored by qix:");
console.table(found);