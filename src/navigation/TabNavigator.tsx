import React, { useEffect, useState } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { Octicons, MaterialCommunityIcons, MaterialIcons } from "@expo/vector-icons/";
import { useAppSelector, useAppDispatch } from "../store/hooks";
import { Image, View } from "react-native";

import LoginScreen from "../screens/Login";
import LevelScreen from "../screens/Level";
import ProgressScreen from "../screens/Progress";

import { HomeStack, ProfileStack, linking } from "./StackNavigator";
import { CustomTabBar } from "./TabBarComponents";

export type TabParams = {
  Home: undefined;
  Chat: undefined;
  Login: undefined;
};

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  const [renderKey, setRenderKey] = useState(0);
  useEffect(() => {
    const timer = setTimeout(() => {
      setRenderKey((prevKey) => prevKey + 1); // Update the key to re-render
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const { isAuthenticated, level, photo } = useAppSelector((state) => state.auth);
  const [imageError, setImageError] = useState(false);

  if (!isAuthenticated) {
    return <LoginScreen />;
  } else if (!level) {
    return <LevelScreen />;
  } else {
    const dispatch = useAppDispatch();

    return (
      <NavigationContainer key={renderKey} linking={linking}>
        <Tab.Navigator
          tabBar={(props) => <CustomTabBar {...props} />}
          screenOptions={{
            tabBarInactiveTintColor: "#B0B0B0",
            tabBarItemStyle: {
              paddingTop: 0,
              height: '100%', // Make sure the item takes full height of the tab bar
              justifyContent: 'center', // Center the icon vertically
            },
            // tabBarActiveTintColor: "#5A4FCF", // Set active color to match your app's theme
          }}
        >
          <Tab.Screen
            name="home"
            component={HomeStack}
            options={{
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <Octicons name="home" size={28} color={color} />
              ),
              tabBarLabel: () => null,
            }}
          />
          <Tab.Screen
            name="progress"
            component={ProgressScreen}
            options={{
              title: "Progress",
              headerShown: false,
              tabBarIcon: ({ color, size }) => (
                <MaterialCommunityIcons
                  name="progress-check"
                  size={30}
                  color={color}
                />
              ),
              tabBarLabel: () => null,
            }}
          />
          <Tab.Screen
            name="profileTab"
            component={ProfileStack}
            options={{
              headerShown: false,
              tabBarIcon: ({ color, size, focused }) => {
                return (
                  photo && !imageError ? (
                    <View style={{
                      width: 30,
                      height: 30,
                      borderRadius: 15,
                      borderWidth: focused ? 2 : 0,
                      borderColor: focused ? '#5A4FCF' : 'transparent',
                      overflow: 'hidden',
                      marginTop: -2,
                    }}>
                      <Image
                        source={{ uri: photo }}
                        style={{
                          width: '100%',
                          height: '100%',
                        }}
                        onError={() => setImageError(true)}
                      />
                    </View>
                  ) : (
                    <MaterialIcons
                      name="account-circle"
                      size={32}
                      color={color}
                    />
                  )
                );
              },
              tabBarLabel: () => null,
            }}
          />
        </Tab.Navigator>
      </NavigationContainer>
    );
  }
};

export default TabNavigator;
