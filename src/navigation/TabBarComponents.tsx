import React from "react";
import { Text, StyleSheet, Platform, Dimensions } from "react-native";
import { BottomTabBar } from "@react-navigation/bottom-tabs";
import { useSafeAreaInsets } from "react-native-safe-area-context";

export const CustomTabBarText = ({ children, focused, color }) => {
  return (
    <Text
      style={{
        fontFamily: "EstedadRegular",
        fontSize: 14,
        color,
      }}
    >
      {children}
    </Text>
  );
};

export const CustomTabBar = (props) => {
  const insets = useSafeAreaInsets();
  const { height: screenHeight } = Dimensions.get('window');

  // Calculate a responsive tab bar height based on device size
  // Minimum height of 50 for smaller devices, and scale up for larger devices
  const baseHeight = Math.max(40, screenHeight * 0.06); // 7% of screen height with minimum of 50

  // Add safe area bottom inset to ensure content doesn't go under home indicator or navigation bar
  const tabBarHeight = baseHeight + insets.bottom;

  return (
    <BottomTabBar
      style={{
        height: tabBarHeight,
        paddingBottom: insets.bottom,
      }}
      {...props}
    />
  );
};
