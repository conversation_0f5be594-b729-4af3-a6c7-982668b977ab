import { useEffect } from "react";
import ChatComponent from "../components/chat/ChatComponent";

export default function Chat({ route, navigation }) {
  useEffect(() => {
    navigation.getParent().setOptions({ tabBarStyle: { display: "none" } });
    return () =>
      navigation.getParent().setOptions({ tabBarStyle: { display: "flex" } });
  }, [navigation]);
  return <ChatComponent route={route} navigation={navigation} />;
}
