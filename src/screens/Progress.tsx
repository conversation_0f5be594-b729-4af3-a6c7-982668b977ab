import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  View,
  StyleSheet,
  Text,
  Animated,
  Pressable,
  ScrollView,
  RefreshControl,
  ImageBackground,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Provider, useDispatch, useSelector } from "react-redux";
import { useAppSelector } from "../store/hooks";
import Svg, { Circle } from "react-native-svg";
import RequestHelper from "../utils/requestHelper";
import { FontAwesome5, Ionicons, MaterialIcons } from "@expo/vector-icons";
import { useFocusEffect, useIsFocused } from "@react-navigation/native";

const getProgress = async (setProgressData, setScores, level, showLoading) => {
  try {
    const { Grammar, Vocabulary, Conversation, average } = await RequestHelper(
      "get",
      `/ai/assessment/average`,
      null,
      null,
      null,
      showLoading
    );

    setScores({
      Grammar: Math.round(Grammar),
      Vocabulary: Math.round(Vocabulary),
      Conversation: Math.round(Conversation),
      average: Math.round(average),
    });

    const {
      gamePoints,
      gameLevel,
      gameGems,
      progress,
      currentLevelPercent,
      streak,
      // rank,
    } = await RequestHelper("get", `/ai/gamification/stats`);
    setProgressData({
      gamePoints,
      gameLevel,
      gameGems,
      progress,
      currentLevelPercent,
      streak,
      // rank,
    });
  } catch (err) {}
};

const CircularProgress = ({
  size,
  strokeWidth,
  duration,
  level,
  currentLevelPercent,
}) => {
  const AnimatedCircle = Animated.createAnimatedComponent(Circle);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const animatedValue = useRef(new Animated.Value(0)).current;

  useFocusEffect(() => {
    animatedValue.setValue(0);
    Animated.timing(animatedValue, {
      toValue: currentLevelPercent, // Progress percentage (0 to 100)
      duration: duration || 1000, // Animation duration in ms
      useNativeDriver: true,
    }).start();
  });

  const strokeDashoffset = animatedValue.interpolate({
    inputRange: [0, 100],
    outputRange: [circumference, 0],
  });

  return (
    <View style={styles.container}>
      <Svg width={size} height={size}>
        {/* Starting Point Dot */}

        {/* Background Circle */}
        <Circle
          stroke="#6b75e6"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
        />

        {/* Progress Circle */}
        <AnimatedCircle
          stroke="#32dcf3"
          cx={size / 2}
          cy={size / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={`${circumference} ${circumference}`}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${size / 2} ${size / 2})`}
        />
        <Circle
          cx={size / 2} // Center of the circle
          cy={size / 2 - radius}
          r={2.5} // Small radius for the dot
          fill="#4e58de" // Color of the dot
        />
        <View style={styles.textContainer}>
          <Text style={styles.pointText}>مرحله</Text>
          <Text style={styles.text}>{level}</Text>
        </View>
      </Svg>
    </View>
  );
};

const App = () => {
  interface Progress {
    gamePoints: number;
    gameLevel: number;
    gameGems: number;
    currentLevelPercent: number;
    streak: number;
    rank: number;
    progress: number;
  }
  interface Scores {
    Grammar: number;
    Vocabulary: number;
    Conversation: number;
    average: number;
  }
  const { level } = useAppSelector((state) => state.auth);
  const [progressData, setProgressData] = useState({} as Progress);
  const [scores, setScores] = useState({} as Scores);
  const [refreshing, setRefreshing] = useState(false);
  const isFocused = useIsFocused();

  useEffect(() => {
    getProgress(setProgressData, setScores, level, true);
  }, []);
  const onRefresh = () => {
    setRefreshing(true);
    getProgress(setProgressData, setScores, level, false);
    setRefreshing(false);
  };
  const stats = [
    {
      label: "گرامر",
      color: "#36dded",
      icon: "book",
      value: scores.Grammar ? `${scores.Grammar}%` : "--",
    },
    {
      label: "واژگان",
      color: "#4e58de",
      icon: "language",
      value: scores.Vocabulary ? `${scores.Vocabulary}%` : "--",
      lib: FontAwesome5,
    },
    {
      label: "میانگین",
      color: "#fe586c",
      icon: "local-fire-department",
      value: scores.average ? `${scores.average}%` : "--",
    },
    {
      label: "مکالمه",
      color: "#64edbf",
      icon: "chatbubbles-sharp",
      lib: Ionicons,
      value: scores.Conversation ? `${scores.Conversation}%` : "--",
    },
  ];
  return (
    <ScrollView
      style={{ backgroundColor: "white" }}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {isFocused && <StatusBar style="light" />}
      <ImageBackground
        source={require("../../assets/progressBackground.png")}
        style={styles.backgroundImage}
      >
        <View style={styles.container}>
          {/* <Text style={styles.title}>میانگین نمرات</Text> */}
          <CircularProgress
            size={180}
            strokeWidth={15}
            level={progressData.gameLevel || "--"}
            currentLevelPercent={progressData.currentLevelPercent || 0}
            duration={1500}
          />
        </View>
        <View style={styles.stats}>
          {stats.map((stat, index) => {
            const IconComponent = stat.lib || MaterialIcons; // Default to Ionicons if pack not found

            return (
              <View
                key={index}
                style={[styles.achievementBox, { backgroundColor: stat.color }]}
              >
                <IconComponent name={stat.icon} size={50} color="white" />
                <View
                  style={{
                    flexDirection: "column",
                    alignItems: "center",
                    // marginLeft: 10,
                    marginTop: 20,
                  }}
                >
                  <Text
                    style={{
                      color: "white",
                      fontSize: 20,
                      // fontFamily: "EstedadRegular",
                    }}
                  >
                    {stat.value}
                  </Text>
                  <Text
                    style={{
                      // fontWeight: "bold",
                      textAlign: "left",
                      fontSize: 16,
                      alignSelf: "flex-start",
                      fontFamily: "EstedadRegular",
                      color: "white",
                    }}
                  >
                    {stat.label}
                  </Text>
                </View>
              </View>
            );
          })}
        </View>
      </ImageBackground>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    resizeMode: "cover", // or 'stretch'
  },
  container: {
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
    // flex: 1,
    // backgroundColor: "#4e58de",
    // paddingVertical: 20,
    paddingTop: 50,
    paddingBottom: 40,
    // borderBottomLeftRadius: 60,
    // borderBottomRightRadius: 60,
  },
  textContainer: {
    // position: "absolute",
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
    marginTop: 35,
  },
  text: {
    fontSize: 35,
    fontWeight: "bold",
    color: "white",
  },
  pointText: {
    fontSize: 20,
    color: "white",
    fontFamily: "EstedadRegular",
  },
  levelsText: {
    fontSize: 15,
    fontWeight: "bold",
    color: "white",
  },
  levels: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    width: "100%",
  },
  achievementBox: {
    fontSize: 15,
    fontWeight: "bold",
    color: "black",
    padding: 10,
    // textShadowColor: "white",
    // backgroundColor: "#ecfdf5",
    // borderLeftColor: "#10b981",
    // borderLeftWidth: 4,
    marginRight: 10,
    width: "46%",
    marginBottom: 10,
    height: 150,
    borderRadius: 15,
    flexDirection: "column",
    alignItems: "center",
    opacity: 0.9,
  },
  title: {
    marginTop: 10,
    fontSize: 20,
    // fontWeight: "bold",
    color: "white",
    // marginBottom: 20,
    fontFamily: "EstedadRegular",
  },
  stats: {
    width: "100%",
    // justifyContent: "space-around",
    alignItems: "center",
    flexDirection: "row",
    flexWrap: "wrap",
    marginBottom: 10,
    padding: "10%",
    borderRadius: 60,
    backgroundColor: "white",
    height: "100%",
    marginTop: -45,
  },
});

export default App;
