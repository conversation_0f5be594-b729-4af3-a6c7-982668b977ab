import RequestHelper from "../utils/requestHelper";
import React, { useEffect, useRef, useState } from "react";
import {
  View,
  Text,
  StatusBar,
  Animated,
} from "react-native";
import { useAppDispatch } from "../store/hooks";
import styles from "../styles/AssessmentScreenStyle";
import { useIsFocused, useNavigation } from "@react-navigation/native";
import { useLayoutEffect } from "react";
import Page1 from "../components/Gamification/Page1";
import Page2 from "../components/Gamification/Page2";
import Page3 from "../components/Gamification/Page3";
import Story from "../components/Gamification/Story";
import TouchLeftRight from "../components/Gamification/TouchLeftRight";
import { updateCoins } from "../store/slices/authSlice";

interface TranscriptionItem {
  type: string;
  text: string;
  isTopic: boolean;
}

interface RewardsData {
  pointsEarned: number;
  gemsEarned: number;
  totalPoints: number;
  totalGems: number;
  gameLevel: number;
  levelProgress: number;
}

interface AssessmentScreenProps {
  route: {
    params: {
      chatId: string;
      topicItem: {
        _id: string;
      };
      transcriptionResult: TranscriptionItem[];
    };
  };
  navigation: any;
}

const AssessmentScreen = ({ navigation, route }: AssessmentScreenProps) => {
  const [scores, setScores] = useState<any>(null);
  const [rewards, setRewards] = useState<RewardsData | null>(null);
  const [page, setPage] = useState(0);
  const dispatch = useAppDispatch();
  const isFocused = useIsFocused();
  const parentNavigation = useNavigation();
  const [barAnimations, setBarAnimations] = useState<Animated.Value[]>([]);

  const [isPaused, setIsPaused] = useState(false);
  const leftIndicatorOpacity = useRef(new Animated.Value(0)).current;
  const rightIndicatorOpacity = useRef(new Animated.Value(0)).current;
  const leftIndicatorScale = useRef(new Animated.Value(1)).current;
  const rightIndicatorScale = useRef(new Animated.Value(1)).current;
  const { chatId, topicItem, transcriptionResult } = route.params;

  const getUserAssessment = async () => {
    try {
      const response = await RequestHelper(
        "post",
        "/ai/assessment",
        {
          chatId,
          scenarioId: topicItem._id,
        },
        null,
        null,
        false
      );
      setScores(response.scores);
      setRewards(response.rewards);
      dispatch(updateCoins(response.rewards.totalGems));
    } catch (error) {
      navigation.goBack();
    }
  };

  useLayoutEffect(() => {
    parentNavigation.getParent()?.setOptions({
      tabBarStyle: { display: "none" }
    });
    return () => {
      parentNavigation.getParent()?.setOptions({
        tabBarStyle: undefined
      });
    };
  }, [parentNavigation]);

  useEffect(() => {
    if (isFocused) {
      setScores(null);
      setPage(0); // Reset to first page (RewardsCard) when screen opens
      getUserAssessment();
    }
  }, [isFocused]);

  if (!scores?.average) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>در حال بارگذاری...</Text>
      </View>
    );
  }

  return (
    <View style={styles.screenContainer}>
      {isFocused && <StatusBar barStyle="light-content" />}

      <Story
        barAnimations={barAnimations}
        page={page}
        setPage={setPage}
        setBarAnimations={setBarAnimations}
        isPaused={isPaused}
      />

      <View style={{ flex: 1 }}>
        <TouchLeftRight
          leftIndicatorScale={leftIndicatorScale}
          leftIndicatorOpacity={leftIndicatorOpacity}
          rightIndicatorScale={rightIndicatorScale}
          rightIndicatorOpacity={rightIndicatorOpacity}
          page={page}
          setPage={setPage}
          setIsPaused={setIsPaused}
          barAnimations={barAnimations}
        />

        {/* Content with swipe gesture */}
        <Animated.View style={[{ flex: 1 }]}>
          {page === 0 && <Page1 />}
          {page === 1 && rewards && <Page2 rewards={rewards} />}
          {page === 2 && rewards && <Page3 scores={scores} />}
        </Animated.View>
      </View>
    </View>
  );
};

export default AssessmentScreen;
