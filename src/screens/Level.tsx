import React from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { Provider, useDispatch, useSelector } from "react-redux";
import { setLevel } from "../store/slices/authSlice";
import RequestHelper from "../utils/requestHelper";
import { LinearGradient } from "expo-linear-gradient";

export default function EnglishLevelScreen() {
  const dispatch = useDispatch();

  const handleSelect = async (level: string) => {
    try {
       await RequestHelper(
        "patch",
        `/users`,
        {
          level,
        }
      );
      dispatch(setLevel({ level }));
    } catch (error) {}
  };

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={["#4b6cb7", "#182848"]}
        style={styles.gradient}
      >
        <Text style={styles.title}>سطح زبان خود را انتخاب کنید</Text>
        <View style={styles.buttonContainer}>
          <TouchableOpacity
            style={styles.button}
            onPress={() => handleSelect("beginner")}
          >
            <Text style={styles.buttonText}>Beginner ( A1 - A2 )</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleSelect("intermediate")}
            style={styles.button}
          >
            <Text style={styles.buttonText}>Intermediate ( B1 - B2 )</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleSelect("advanced")}
            style={styles.button}
          >
            <Text style={styles.buttonText}>Advanced ( C1 - C2 )</Text>
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // justifyContent: "center",
    // alignItems: "center",
    // paddingHorizontal: 20,
    // backgroundColor: "#2980b9", // Start of gradient
  },
  gradient: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 20,
  },
  title: {
    fontSize: 28,
    // fontWeight: "bold",
    fontFamily: "EstedadRegular",
    color: "white",
    marginBottom: 40,
  },
  buttonContainer: {
    width: "100%",
  },
  button: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 10,
    marginBottom: 20,
    alignItems: "center",
  },
  buttonText: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#4b6cb7", // Match the main background color
  },
});
