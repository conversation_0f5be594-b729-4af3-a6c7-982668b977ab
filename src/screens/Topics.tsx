import React, { useEffect, useState, memo } from "react";
import {
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableHighlight,
} from "react-native";
import RequestHelper from "../utils/requestHelper";
import { isSpeaking, stopSpeech } from "../utils/speechUtils";
import { useFocusEffect } from "@react-navigation/native";
import { useAppSelector, useAppDispatch } from "../store/hooks";
import {
  MaterialIcons,
  FontAwesome5,
  FontAwesome,
  MaterialCommunityIcons,
} from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import LargeProgressBar from "../components/progress/LargeProgressBar";
import { initNewChat } from "../store/slices/modelSlice";
import { updateCoins } from "../store/slices/authSlice";
import ScenarioLockModal from "../components/modals/ScenarioLockModal";
import AsyncStorage from "@react-native-async-storage/async-storage";
import InsufficientCoinsModal from "../components/modals/InsufficientCoinsModal";

interface Scenario {
  _id: string;
  title: string;
  translated_title: string;
  ai_role: string;
  user_role: string;
  scenario: string;
  level: string;
  icon: string;
  icon_pack: string;
  isPassed?: boolean;
  scoreAverage?: number;
  scenarioProgress?: number;
  merged?: string;
}

interface Data {
  scenarios: Scenario[];
}

interface RouteParams {
  refresh?: boolean;
  lastTopicId?: string;
}

interface NavigationProps {
  navigate: (screen: string, params?: any) => void;
  goBack: () => void;
  getParent: () => any;
  setOptions: (options: any) => void;
}

const App = ({
  route,
  navigation,
}: {
  route: { params?: RouteParams };
  navigation: NavigationProps;
}) => {
  const [data, setData] = useState<Data>();
  const dispatch = useAppDispatch();
  const { level, isPremium, coins } = useAppSelector((state) => state.auth);
  const [progress, setProgress] = useState(0);
  const [refreshing, setRefreshing] = useState(false);
  const [lastVisitedId, setLastVisitedId] = useState<string | null>(null);
  const [isLockModalVisible, setIsLockModalVisible] = useState(false);
  const [gemCostPerLesson, setGemCostPerLesson] = useState(0);
  const [isInsufficientCoinsModalVisible, setIsInsufficientCoinsModalVisible] =
    useState(false);

  useEffect(() => {
    getScenarios();
  }, [level]);

  useEffect(() => {
    if (route.params?.refresh) {
      getScenarios();
      dispatch(initNewChat({ newChat: false }));
      if (route.params?.lastTopicId) {
        setLastVisitedId(route.params.lastTopicId);
      }
    }
  }, [route]);

  useEffect(() => {
    navigation.getParent().setOptions({ tabBarStyle: { display: "none" } });
    return () =>
      navigation.getParent().setOptions({ tabBarStyle: { display: "flex" } });
  }, [navigation]);

  const checkIfSpeaking = async () => {
    const speaking = await isSpeaking();
    if (speaking) {
      stopSpeech();
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      setTimeout(() => {
        checkIfSpeaking();
      }, 1000);
    }, [])
  );

  const getScenarios = async () => {
    setRefreshing(true);
    try {
      const {
        scenarios,
        progress: progressData,
        GemCostPerLesson,
      } = await RequestHelper(
        "get",
        `/v3/ai/scenarios/?level=${level}`,
        null,
        null,
        null,
        false
      );
      setRefreshing(false);
      setData({ scenarios });
      setProgress(progressData);
      setGemCostPerLesson(GemCostPerLesson);
    } catch (err) {
      setRefreshing(false);
    }
  };

  const checkScenarioAccess = async (item: Scenario) => {
    // Premium users can access any scenario
    if (isPremium) {
      navigateToScenario(item);
      return;
    }

    // Get the index of the current scenario in the list
    const scenarioIndex =
      data?.scenarios.findIndex((scenario) => scenario._id === item._id) || 0;
    const currentScenario = data?.scenarios[scenarioIndex];
    if (currentScenario) {
      navigateToScenario(item);
      return;
    }

    // If it's the first scenario, allow access
    if (scenarioIndex === 0) {
      navigateToScenario(item);
      return;
    }

    // Check if previous scenario has been completed
    try {
      // const completedTopicsStr = await AsyncStorage.getItem("completedTopics");
      // const completedTopics = completedTopicsStr
      //   ? JSON.parse(completedTopicsStr)
      //   : [];

      // Get the previous scenario
      const previousScenario = data?.scenarios[scenarioIndex - 1];

      // If previous scenario has a score, allow access
      if (
        previousScenario &&
        previousScenario &&
        previousScenario.scoreAverage > 0
      ) {
        navigateToScenario(item);
      } else {
        // Show lock modal
        setIsLockModalVisible(true);
      }
    } catch (error) {
      console.log("Error checking scenario access:", error);
      // In case of error, allow access
      navigateToScenario(item);
    }
  };

  const navigateToScenario = (item: Scenario) => {
    item.merged = `Conversation Title: ${item.title}
    Role of Ai:  ${item.ai_role}
    Role of user: ${item.user_role}
    Conversation scenario: ${item.scenario}
    Answer very short and briefly as ${item.ai_role}.
    Do not response with more than 1 sentence.
    Start with a question.
    Don't start the paragraph with Role of Ai or Role of user.
    Don't mention the roles.
    Avoid starting sentence with discourse markers such as absolutely, okay, certainly
    Proficiency Level in English Speaking: ${item.level}`;
    setLastVisitedId(item._id);
    navigation.navigate("chat", { topicItem: item });
  };

  const itemClicked = async (item: Scenario) => {
      if (isPremium || coins >= gemCostPerLesson) {
        checkScenarioAccess(item);
      } else {
        setIsInsufficientCoinsModalVisible(true);
      }
  };

  const handleSubscribe = () => {
    setIsLockModalVisible(false);
    navigation.navigate("subscription");
  };

  function generateGradientColors() {
    const baseColor = Math.floor(Math.random() * 360); // Pick a base hue
    const color1 = `hsl(${baseColor}, 70%, 50%)`; // First color
    const color2 = `hsl(${(baseColor + 30) % 360}, 70%, 50%)`; // Second color with offset hue

    return [color1, color2];
  }

  function ProgressBarComponent({ item }: { item: Scenario }) {
    if (item.scoreAverage) {
      return <Text style={styles.score}>{item.scoreAverage}</Text>;
    }
    return null;
  }

  const Item = memo(({ item }: { item: Scenario }) => {
    const iconPackMap = {
      MaterialIcons,
      FontAwesome,
      FontAwesome5,
      MaterialCommunityIcons,
    };

    type IconPackName = keyof typeof iconPackMap;

    type IconProps = {
      pack: string;
      name: string;
      size: number;
      color: string;
      style?: any;
    };

    const DynamicIcon = ({ pack, name, size, color, style }: IconProps) => {
      // Safely cast the pack string to our known icon pack names or default to MaterialIcons
      const safeIconPack = (
        Object.keys(iconPackMap).includes(pack) ? pack : "MaterialIcons"
      ) as IconPackName;
      const IconComponent = iconPackMap[safeIconPack];
      return (
        <IconComponent name={name} size={size} color={color} style={style} />
      );
    };
    return (
      <View style={styles.marginWrapper}>
        <TouchableHighlight
          onPress={() => itemClicked(item)}
          activeOpacity={0.6}
          underlayColor="#DDDDDD"
        >
          <View
            style={[
              styles.card,
              lastVisitedId === item._id &&
                !item.scoreAverage &&
                styles.highlightedCard,
            ]}
          >
            <View style={{ flexDirection: "row", alignItems: "center" }}>
              <LinearGradient
                colors={generateGradientColors()}
                start={{ x: 0, y: 0 }}
                end={{ x: 1, y: 1 }}
                style={{
                  marginRight: 10,
                  marginLeft: 5,
                  width: 60,
                  height: 60,
                  borderRadius: 100,
                }}
              >
                <DynamicIcon
                  pack={item.icon_pack}
                  name={item.icon}
                  size={35}
                  color="white"
                  style={{ alignSelf: "center", marginTop: 9 }}
                />
              </LinearGradient>

              <View
                style={{
                  flexDirection: "column",
                  justifyContent: "flex-start",
                  maxWidth: 200,
                  marginLeft: 7,
                }}
              >
                <Text
                  style={[
                    styles.title,
                    { color: item.isPassed ? "#000" : "#000" },
                  ]}
                >
                  {item.title}
                </Text>
                <Text
                  style={[
                    styles.description,
                    { color: item.isPassed ? "#000" : "#666" },
                  ]}
                >
                  {item.translated_title}
                </Text>
              </View>
            </View>
            <View style={{ width: "15%" }}>
              <ProgressBarComponent item={item} />
            </View>
          </View>
        </TouchableHighlight>
      </View>
    );
  });

  return (
    <LinearGradient
      style={styles.gradient}
      colors={["rgb(213, 213, 246)", "rgb(60, 60, 246)", "rgb(89, 38, 190)"]}
    >
      <View
        style={{
          paddingHorizontal: 20,
          paddingTop: 20,
        }}
      >
        <FlatList
          data={data?.scenarios}
          renderItem={({ item }: { item: Scenario }) => <Item item={item} />}
          keyExtractor={(item: Scenario) => item._id}
          onRefresh={() => {
            getScenarios();
          }}
          refreshing={refreshing}
          showsVerticalScrollIndicator={false}
          ListHeaderComponent={() => {
            return (
              <LargeProgressBar progress={progress} refreshing={refreshing} />
            );
          }}
        />
      </View>

      {/* Lock Modal */}
      <ScenarioLockModal
        isVisible={isLockModalVisible}
        onClose={() => setIsLockModalVisible(false)}
        onSubscribe={handleSubscribe}
      />
      <InsufficientCoinsModal
        visible={isInsufficientCoinsModalVisible}
        requiredCoins={gemCostPerLesson}
        onClose={() => setIsInsufficientCoinsModalVisible(false)}
        onSubscribe={() => {
          setIsInsufficientCoinsModalVisible(false);
          navigation.navigate("subscription");
        }}
      />
    </LinearGradient>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // paddingTop: 20,
    // backgroundColor: "rgb(71, 120, 176)",
  },
  gradient: {
    flex: 1,
  },
  card: {
    backgroundColor: "white",
    borderRadius: 5,
    padding: 15,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 2.22,
  },
  marginWrapper: {
    marginBottom: 10,
  },
  title: {
    fontSize: 16,
    fontWeight: "bold",
  },
  description: {
    marginTop: 7,
    fontSize: 15,
    color: "#666",
    fontFamily: "EstedadRegular",
    textAlign: "left",
  },
  score: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#fff",
    backgroundColor: `hsl(${Math.floor(Math.random() * 360)}, 70%, 50%)`,
    borderRadius: 50,
    padding: 10.5,
    textAlign: "center",
    width: 40,
    height: 40,
    lineHeight: 20,
  },
  progressBar: {
    width: "100%",
    height: 4,
    backgroundColor: "#E0E0E0",
    borderRadius: 2,
    overflow: "hidden",
  },
  progressFill: {
    height: "100%",
    backgroundColor: "#2196F3",
  },
  highlightedCard: {
    // borderWidth: 4,
    // borderColor: "#e0e800", // Orange to contrast with blue.
    backgroundColor: "#cffcf7",
  },
});

export default App;
