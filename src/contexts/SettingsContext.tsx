import React, { createContext, useContext, useEffect, useState } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { getAvailableVoices, getDefaultVoice } from "../utils/voiceUtils";
import { Voice } from "../utils/voiceUtils";

interface SettingsContextType {
  selectedVoice: string | null;
  setSelectedVoice: (voiceId: string) => void;
  availableVoices: Voice[];
  loading: boolean;
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [selectedVoice, setSelectedVoiceState] = useState<string | null>(null);
  const [availableVoices, setAvailableVoices] = useState<Voice[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const initializeSettings = async () => {
      try {
        // Get available voices
        const voices = await getAvailableVoices();
        setAvailableVoices(voices);
        
        // Get saved voice preference
        const savedVoice = await AsyncStorage.getItem("selectedVoice");
        if (savedVoice) {
          setSelectedVoiceState(savedVoice);
        } else {
          // Set default voice if none saved
          const defaultVoice = await getDefaultVoice();
          if (defaultVoice) {
            setSelectedVoiceState(defaultVoice);
          }
        }
      } catch (error) {
        console.error("Error initializing settings:", error);
      } finally {
        setLoading(false);
      }
    };

    initializeSettings();
  }, []);

  const setSelectedVoice = async (voiceId: string) => {
    try {
      await AsyncStorage.setItem("selectedVoice", voiceId);
      setSelectedVoiceState(voiceId);
    } catch (error) {
      console.error("Error saving selected voice:", error);
    }
  };

  return (
    <SettingsContext.Provider
      value={{
        selectedVoice,
        setSelectedVoice,
        availableVoices,
        loading,
      }}
    >
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error("useSettings must be used within a SettingsProvider");
  }
  return context;
};