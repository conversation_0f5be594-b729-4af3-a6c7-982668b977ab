import * as Speech from "expo-speech";
import AsyncStorage from "@react-native-async-storage/async-storage";

interface SpeechOptions {
  rate?: number;
  pitch?: number;
  language?: string;
  voice?: string;
  onDone?: () => void;
  onError?: (error: any) => void;
}

export const speakWithSelectedVoice = async (
  text: string,
  options: SpeechOptions = {},
) => {
  try {
    // Get the selected voice from AsyncStorage
    const selectedVoice = await AsyncStorage.getItem("selectedVoice");
    
    // Get the selected speech rate from AsyncStorage
    const savedRate = await AsyncStorage.getItem("speechRate");
    const speechRate = savedRate ? parseFloat(savedRate) : 0.8;
    
    // Default options
    const defaultOptions: SpeechOptions = {
      rate: speechRate,
      pitch: 1,
      language: "en-US",
      ...options,
    };
    
    // Add voice to options if available
    if (selectedVoice) {
      defaultOptions.voice = selectedVoice;
    }
    
    // Speak with the selected voice and rate
    // Add onDone callback to detect when speech completes
    const speechOptions = {
      ...defaultOptions,
      onDone: () => {
        
        if (options.onDone) {
          options.onDone();
        }
      }
    };
    
    Speech.speak(text, speechOptions);
  } catch (error) {
    console.error("Error speaking with selected voice:", error);
    // Fallback to default speech if there's an error
    Speech.speak(text, options);
  }
};

export const stopSpeech = () => {
  Speech.stop();
};

export const isSpeaking = async (): Promise<boolean> => {
  return await Speech.isSpeakingAsync();
};