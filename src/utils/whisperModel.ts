import { InferenceSession, Tensor } from "onnxruntime-react-native";
import { createSession, setTextOfSpeech } from "../store/slices/modelSlice";
import store from "../store/store";
import { Asset } from "expo-asset";

export async function loadModel() {
  try {
    let modelUri;
    if (__DEV__) {
      const localUri =
        "file:///data/user/0/fa.speakup.ai.debug/cache/whisper_cpu_int8_cpu-cpu_model_base_ignored.onnx";
      modelUri = localUri;
    } else {
      const modelPath = require("../../assets/whisper_cpu_int8_cpu-cpu_model_base_ignored.onnx");
      const assets = await Asset.loadAsync(modelPath);
      modelUri = assets[0].localUri;
    }

    if (!modelUri) {
      console.error("no modelUri");
    } else {
      const NewSession = await InferenceSession.create(modelUri, {
        // executionProviders: ["cpu", "nnapi"],
      });
      store.dispatch(createSession({ Session: NewSession }));
    }
  } catch (e) {
    // console.log("failed to load model", `${e}`);
  }
}

export async function runModel(audioBuffer: any[], beams = 1, screenId?: string) {
  // const { Session } = useAppSelector((state) => state.model);
  const {
    model: { Session },
  } = store.getState();
  try {
    const min_length = Int32Array.from({ length: 1 }, () => 1);
    const max_length = Int32Array.from({ length: 1 }, () => 448);
    const num_return_sequences = Int32Array.from({ length: 1 }, () => 1);
    const length_penalty = Float32Array.from({ length: 1 }, () => 1);
    const repetition_penalty = Float32Array.from({ length: 1 }, () => 1);

    const feed = {
      audio_pcm: new Tensor(audioBuffer, [1, audioBuffer.length]),
      max_length: new Tensor(new Int32Array(max_length), [1]),
      min_length: new Tensor(new Int32Array(min_length), [1]),
      num_beams: new Tensor(
        Int32Array.from({ length: 1 }, () => beams),
        [1]
      ),
      num_return_sequences: new Tensor(new Int32Array(num_return_sequences), [
        1,
      ]),
      length_penalty: new Tensor(new Float32Array(length_penalty), [1]),
      repetition_penalty: new Tensor(new Float32Array(repetition_penalty), [1]),
    };
    const fetches = await Session.run(feed);
    const output = fetches[Session.outputNames[0]];
    const outputText = output.data[0];
    if (
      typeof outputText == "string" &&
      outputText !== "" &&
      outputText.length < 300
    ) {
      store.dispatch(setTextOfSpeech({ textOfSpeech: outputText }));
    }
    return true;
  } catch (e) {
    console.log("failed to run model", `${e}`);
    throw e;
  }
}
