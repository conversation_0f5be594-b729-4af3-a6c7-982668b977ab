import {
  Linking,
} from "react-native";
import * as Clipboard from "expo-clipboard";

export default async ({ eventType, content, selectionStart, selectionEnd }) => {
  if (eventType === "Translate") {
    const translateURL = `https://translate.google.com/?hl=en&tab=wT&sl=en&tl=fa&text=${content}%0A&op=translate`;
    return Linking.openURL(translateURL);
  }
  if (eventType === "Copy") {
    Clipboard.setStringAsync(content);
    const text = await Clipboard.getStringAsync();
  }
};
