/**
 * Compares two texts and returns an array of segments with information about whether each segment is different
 * @param originalText The original text
 * @param correctedText The corrected text
 * @returns An array of segments with text and isDifferent flag
 */
export interface TextSegment {
  text: string;
  isDifferent: boolean;
}

export const compareTexts = (originalText: string, correctedText: string): TextSegment[] => {
  // Simple word-by-word comparison
  const originalWords = originalText.split(/\s+/);
  const correctedWords = correctedText.split(/\s+/);
  
  const result: TextSegment[] = [];
  let currentSegment: TextSegment | null = null;
  
  // Find the longest common subsequence length
  const maxLength = Math.max(originalWords.length, correctedWords.length);
  
  for (let i = 0; i < maxLength; i++) {
    const originalWord = i < originalWords.length ? originalWords[i] : '';
    const correctedWord = i < correctedWords.length ? correctedWords[i] : '';
    
    // Check if words are different (ignoring case and punctuation)
    const cleanOriginal = originalWord.toLowerCase().replace(/[.,!?;:]/g, '');
    const cleanCorrected = correctedWord.toLowerCase().replace(/[.,!?;:]/g, '');
    const isDifferent = cleanOriginal !== cleanCorrected && cleanOriginal !== '' && cleanCorrected !== '';
    
    // If we have a current segment of the same type, append to it
    if (currentSegment && currentSegment.isDifferent === isDifferent) {
      currentSegment.text += (currentSegment.text ? ' ' : '') + correctedWord;
    } else {
      // Start a new segment
      if (currentSegment) {
        result.push(currentSegment);
      }
      currentSegment = {
        text: correctedWord,
        isDifferent: isDifferent
      };
    }
  }
  
  // Add the last segment
  if (currentSegment) {
    result.push(currentSegment);
  }
  
  return result;
};
