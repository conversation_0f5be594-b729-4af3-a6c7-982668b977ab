function getTextAfterColon(input: string) {
  let colonIndex = input.indexOf(":");
  return colonIndex !== -1 ? input.substring(colonIndex + 1).trim() : input;
}

const findWordIndex = (sentence: string, sign: string) => {
  const words = sentence.split(/\s+/);
  const dotIndex = words.findIndex((word) => word.includes(sign));
  const remaingText = words.slice(dotIndex + 1, words.length).join(" ");
  return { dotIndex, words, remaingText };
};

function trimAfterDot(sentence: string, level: string) {
  const { dotIndex, words, remaingText } = findWordIndex(sentence, ".");
  if (dotIndex === -1) {
    return sentence;
  }
  if (dotIndex > (level === "beginner" ? 6 : 10)) {
    words[dotIndex] = words[dotIndex].split(".")[0];
    return words.slice(0, dotIndex + 1).join(" ");
  }
  const dotIndex1Length = words.length - dotIndex;
  if (dotIndex1Length > (level === "beginner" ? 10 : 15)) {
    const { dotIndex: dotIndex2, words: words2 } = findWordIndex(
      remaingText,
      "."
    );
    const { dotIndex: questionIndex } = findWordIndex(remaingText, "?");
    if (dotIndex2 > 0 || questionIndex > 0) {
      return words
        .slice(
          0,
          dotIndex +
            1 +
            (dotIndex2 > questionIndex ? dotIndex2 : questionIndex) +
            1
        )
        .join(" ");
    }
    return words.slice(0, dotIndex + 1).join(" ");
  }
  return sentence;
}

export default (string: string, level: string) => {
  const removeSymbols = string.replace(/[^a-zA-Z0-9 _.?!,:'$€%\n-]/g, "");
  const removeSpecialCharsAtFirst = removeSymbols.replace(/^[^a-zA-Z0-9]+/, "");
  if (level != "beginner") {
    return removeSpecialCharsAtFirst;
  }
  const trimEmptyLines = removeSpecialCharsAtFirst.replace(/^\s*[\r\n]/gm, "");
  const fistLine = trimEmptyLines.split("\n", 1)[0];
  const textAfterColon = getTextAfterColon(fistLine);
  const textWithoutAsterisk = textAfterColon.replace(/\*/g, "");
  return trimAfterDot(textWithoutAsterisk, level);
};
