import axios from "axios";
import store from "../store/store";
import { showLoading, hideLoading } from "../store/slices/loadingSlice";
import axiosRetry from "axios-retry";
import Toast from "react-native-toast-message";

let BaseURL = "https://api.speakapp.ir";
// if (__DEV__) {
//   BaseURL = "http://192.168.100.2:3003";
// }

export default async (
  method: string,
  url: string,
  data: any = null,
  headers: {} | null = null,
  params = null,
  showLoader = false
) => {
  const {
    auth: { token },
  } = store.getState();
  if (showLoader) {
    store.dispatch(showLoading());
  }
  try {
    axiosRetry(axios, {
      retries: 3,
      onRetry: (retryCount, error) => {
        console.log(
          `Retry attempt #${retryCount} due to error:`,
          error?.message
        );
      },
    });
    const { data: datas } = await axios({
      method,
      url: BaseURL + url,
      data,
      headers: {
        Authorization: token,
        ...headers,
      },
      // params,
      timeout: 15000,
    });
    if (showLoader) {
      store.dispatch(hideLoading());
    }
    return datas;
  } catch (err) {
      console.log("Error Message:", err);

    if (err?.response) {
      console.log("Error Status:", err?.response?.status);
      console.log("Error Data:", err?.response?.data);
    } else {
      console.log("Error Message:", err);
    }
    if (showLoader) {
      store.dispatch(hideLoading());
    }
    let errorMessage = err?.response?.data.message || "خطایی رخ داد";
    Toast.show({
      type: "error",
      text2: errorMessage,
      bottomOffset: 100,
      position: "bottom",
    });
    throw new Error(errorMessage);
  }
};
