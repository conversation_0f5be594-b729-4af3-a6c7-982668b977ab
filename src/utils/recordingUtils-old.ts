import AudioRecord from "react-native-audio-record";
import { EmitterSubscription, PermissionsAndroid } from "react-native";
import RNFS from "react-native-fs";
import wav from "node-wav";
import { <PERSON>uff<PERSON> } from "buffer";
import { runModel } from "./whisperModel";

// Constants for silence detection
const SILENCE_THRESHOLD = 0.005;
const SILENCE_DURATION = 4000; // 2 seconds of silence before stopping
const MIN_SPEECH_DURATION = 500; // Minimum duration to consider as speech
let lastSilenceTime: number | null = null;
let userIsSpeaking = false;

// Request microphone permission
export async function requestMicrophonePermission() {
  try {
    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
      {
        title: "Microphone Permission",
        message: "This app needs access to your microphone.",
        buttonPositive: "OK",
        buttonNegative: "Cancel",
      }
    );
    return granted === PermissionsAndroid.RESULTS.GRANTED;
  } catch (err) {
    console.warn(err);
    return false;
  }
}

// Check microphone permission
export async function checkMicrophonePermission() {
  try {
    const result = await PermissionsAndroid.check(
      PermissionsAndroid.PERMISSIONS.RECORD_AUDIO
    );
    if (result) {
      return true;
    }
    return await requestMicrophonePermission();
  } catch (err) {
    console.warn(err);
    return false;
  }
}

// Initialize recording with options
export async function initRecording() {
  const options = {
    sampleRate: 16000,
    channels: 1,
    bitsPerSample: 16,
    audioSource: 6,
    wavFile: "test.wav",
  };
  
  try {
    const hasPermission = await checkMicrophonePermission();
    if (!hasPermission) {
      throw new Error("Microphone permission not granted");
    }
    AudioRecord.init(options);
    return true;
  } catch (error) {
    console.error("Error initializing recording:", error);
    return false;
  }
}

// Detect silence in audio data and manage speech detection
export function detectSilence(data: string, onSilenceDetected?: () => void, onSpeechDetected?: () => void) {
  try {
    const binary = atob(data);
    const byteArray = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) {
      byteArray[i] = binary.charCodeAt(i);
    }

    const int16Array = new Int16Array(byteArray.buffer);
    let sumSquares = 0;
    for (let i = 0; i < int16Array.length; i++) {
      const normalized = int16Array[i] / 32768.0;
      sumSquares += normalized * normalized;
    }
    const rms = Math.sqrt(sumSquares / int16Array.length);
    const currentTime = Date.now();
    const isSilent = rms < SILENCE_THRESHOLD;

    if (isSilent) {
      if (!lastSilenceTime) {
        lastSilenceTime = currentTime;
      } else if (currentTime - lastSilenceTime >= SILENCE_DURATION && userIsSpeaking) {
        userIsSpeaking = false;
        if (onSilenceDetected) onSilenceDetected();
      }
    } else {
      if (!userIsSpeaking) {
        userIsSpeaking = true;
        if (onSpeechDetected) onSpeechDetected();
      }
      lastSilenceTime = null;
    }

    return isSilent;
  } catch (error) {
    console.error("Error processing audio data:", error);
    return false;
  }
}

// Load and process WAV file
export async function loadWav() {
  try {
    const buffer = await RNFS.readFile(
      RNFS.DocumentDirectoryPath + "/test.wav",
      "base64"
    );
    const byteArray = Buffer.from(buffer, "base64");
    const result = wav.decode(byteArray);
    // return await runModel(result.channelData[0]);
  } catch (error) {
    console.error("Error loading WAV file:", error);
    return null;
  }
}

// Start recording with continuous monitoring
export function startRecording(
  onSilenceDetected: () => void,
  onSpeechDetected: () => void
) {
  try {
    lastSilenceTime = null;
    userIsSpeaking = false;
    AudioRecord.start();
    return AudioRecord.on("data", (data: string) => {
      detectSilence(data, onSilenceDetected, onSpeechDetected);
    });
  } catch (error) {
    console.error("Error starting recording:", error);
    throw error;
  }
}

// Stop recording and transcribe
export async function stopRecording() {
  try {
    await AudioRecord.stop();
    return await loadWav();
  } catch (error) {
    console.error("Error stopping recording:", error);
    throw error;
  }
}