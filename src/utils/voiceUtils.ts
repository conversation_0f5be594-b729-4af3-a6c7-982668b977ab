import * as Speech from "expo-speech";

export interface Voice {
  id: string;
  name: string;
  language: string;
  quality: number;
  latency: number;
}

export const getAvailableVoices = async (): Promise<Voice[]> => {
  try {
    const voices = await Speech.getAvailableVoicesAsync();
    // Filter for English voices (including variants like en-US, en-GB, en-AU, etc.)
    const englishVoices = voices.filter((voice: any) =>
      voice.language && voice.language.toLowerCase().startsWith("en")
    );
    // If no English voices found, return all voices
    const voicesToUse = englishVoices.length > 0 ? englishVoices : voices;
    return voicesToUse.map((voice: any) => ({
      id: voice.identifier || voice.id,
      name: voice.name,
      language: voice.language,
      quality: voice.quality || 0,
      latency: voice.latency || 0,
    }));

  } catch (error) {
    console.error("Error getting available voices:", error);
    return [];
  }
};

export const getDefaultVoice = async (): Promise<string | null> => {
  try {
    const voices = await getAvailableVoices();
    if (voices.length > 0) {
      // Return the first voice as default
      return voices[0].id;
    }
    return null;
  } catch (error) {
    console.error("Error getting default voice:", error);
    return null;
  }
};