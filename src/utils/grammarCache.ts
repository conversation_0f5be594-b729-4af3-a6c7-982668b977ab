interface CacheEntry {
  explanation?: string;
  refinements?: string[];
  timestamp: number;
}

class GrammarCache {
  private static cache: Map<string, CacheEntry> = new Map();
  private static readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

  static setGrammarExplanation(text: string, explanation: string): void {
    const existingEntry = this.cache.get(text) || { timestamp: Date.now() };
    this.cache.set(text, {
      ...existingEntry,
      explanation,
      timestamp: Date.now(),
    });
  }

  static setRefinements(text: string, refinements: string[]): void {
    const existingEntry = this.cache.get(text) || { timestamp: Date.now() };
    this.cache.set(text, {
      ...existingEntry,
      refinements,
      timestamp: Date.now(),
    });
  }

  static getGrammarExplanation(text: string): string | null {
    const entry = this.cache.get(text);
    if (!entry || !entry.explanation) return null;
    
    if (Date.now() - entry.timestamp > this.CACHE_DURATION) {
      this.cache.delete(text);
      return null;
    }
    
    return entry.explanation;
  }

  static getRefinements(text: string): string[] | null {
    const entry = this.cache.get(text);
    if (!entry || !entry.refinements) return null;
    
    if (Date.now() - entry.timestamp > this.CACHE_DURATION) {
      this.cache.delete(text);
      return null;
    }
    
    return entry.refinements;
  }

  static clearCache(): void {
    this.cache.clear();
  }
}

export default GrammarCache;