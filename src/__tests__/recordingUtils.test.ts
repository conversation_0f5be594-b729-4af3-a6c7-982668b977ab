import { requestMicrophonePermission, checkMicrophonePermission, initRecording, detectSilence, loadWav, startRecording, stopRecording } from '../utils/recordingUtils-old';
import { PermissionsAndroid } from 'react-native';
import AudioRecord from 'react-native-audio-record';

jest.mock('react-native', () => ({
  PermissionsAndroid: {
    request: jest.fn(),
    PERMISSIONS: {
      RECORD_AUDIO: 'android.permission.RECORD_AUDIO'
    },
    RESULTS: {
      GRANTED: 'granted',
      DENIED: 'denied'
    }
  },
  Platform: {
    OS: 'android'
  }
}));

jest.mock('react-native-audio-record', () => ({
  init: jest.fn(),
  start: jest.fn(),
  stop: jest.fn(),
  on: jest.fn()
}));

describe('recordingUtils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('requestMicrophonePermission', () => {
    it('should request and return microphone permission status', async () => {
      (PermissionsAndroid.request as jest.Mock).mockResolvedValue('granted');
      const result = await requestMicrophonePermission();
      expect(result).toBe(true);
      expect(PermissionsAndroid.request).toHaveBeenCalledWith(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO
      );
    });

    it('should return false when permission is denied', async () => {
      (PermissionsAndroid.request as jest.Mock).mockResolvedValue('denied');
      const result = await requestMicrophonePermission();
      expect(result).toBe(false);
    });
  });

  describe('initRecording', () => {
    it('should initialize audio recording with correct options', async () => {
      await initRecording();
      expect(AudioRecord.init).toHaveBeenCalledWith({
        sampleRate: 16000,
        channels: 1,
        bitsPerSample: 16,
        audioSource: 6,
        wavFile: 'test.wav'
      });
    });
  });

  describe('detectSilence', () => {
    it('should detect silence when audio data is below threshold', () => {
      const mockSilenceCallback = jest.fn();
      const mockSpeechCallback = jest.fn();
      const silentData = '0000000000'; // Simulating silent audio data

      detectSilence(silentData, mockSilenceCallback, mockSpeechCallback);
      expect(mockSilenceCallback).toHaveBeenCalled();
      expect(mockSpeechCallback).not.toHaveBeenCalled();
    });

    it('should detect speech when audio data is above threshold', () => {
      const mockSilenceCallback = jest.fn();
      const mockSpeechCallback = jest.fn();
      const speechData = 'FFFFFFFFFF'; // Simulating speech audio data

      detectSilence(speechData, mockSilenceCallback, mockSpeechCallback);
      expect(mockSpeechCallback).toHaveBeenCalled();
      expect(mockSilenceCallback).not.toHaveBeenCalled();
    });
  });

  describe('startRecording', () => {
    it('should start audio recording with correct callbacks', () => {
      const mockOnSilence = jest.fn();
      const mockOnSpeech = jest.fn();

      startRecording(mockOnSilence, mockOnSpeech);
      expect(AudioRecord.start).toHaveBeenCalled();
      expect(AudioRecord.on).toHaveBeenCalledWith('data', expect.any(Function));
    });
  });

  describe('stopRecording', () => {
    it('should stop audio recording', async () => {
      await stopRecording();
      expect(AudioRecord.stop).toHaveBeenCalled();
    });
  });
});