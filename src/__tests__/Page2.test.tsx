import React from 'react';
import { render } from '@testing-library/react-native';
import RewardsCard from '../components/Gamification/Page2';

// Mock the useFocusEffect hook
jest.mock('@react-navigation/native', () => ({
  useFocusEffect: jest.fn((callback) => callback()),
}));

describe('RewardsCard Component', () => {
  it('renders correctly with rewards data', () => {
    const mockRewards = {
      pointsEarned: 95,
      gemsEarned: 15,
      totalPoints: 500,
      totalGems: 100,
      gameLevel: 7,
      levelProgress: 100,
    };

    const { toJSON } = render(<RewardsCard rewards={mockRewards} />);
    expect(toJSON()).toBeTruthy();
  });

  it('returns null when no rewards are provided', () => {
    const { toJSON } = render(<RewardsCard rewards={null} />);
    expect(toJSON()).toBeNull();
  });
});
