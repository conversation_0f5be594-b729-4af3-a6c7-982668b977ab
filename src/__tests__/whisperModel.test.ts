import { loadModel, runModel } from '../utils/whisperModel';
import * as RNFS from 'react-native-fs';
import { InferenceSession } from 'onnxruntime-react-native';

jest.mock('react-native-fs', () => ({
  readFile: jest.fn(),
  exists: jest.fn(),
  DocumentDirectoryPath: '/mock/path',
}));

jest.mock('onnxruntime-react-native', () => ({
  InferenceSession: {
    create: jest.fn(),
  },
}));

describe('whisperModel', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('loadModel', () => {
    it('should load the model successfully', async () => {
      const mockSession = {
        run: jest.fn(),
      };
      (InferenceSession.create as jest.Mock).mockResolvedValue(mockSession);
      (RNFS.exists as jest.Mock).mockResolvedValue(true);

      const result = await loadModel();
      expect(result).toBe(true);
      expect(InferenceSession.create).toHaveBeenCalled();
    });

    it('should handle model loading failure', async () => {
      (InferenceSession.create as jest.Mock).mockRejectedValue(new Error('Model loading failed'));
      (RNFS.exists as jest.Mock).mockResolvedValue(true);

      const result = await loadModel();
      expect(result).toBe(false);
    });
  });

  describe('runModel', () => {
    it('should process audio buffer and return transcription', async () => {
      const mockAudioBuffer = [1, 2, 3];
      const mockSession = {
        run: jest.fn().mockResolvedValue({
          text: ['sample transcription']
        })
      };
      (InferenceSession.create as jest.Mock).mockResolvedValue(mockSession);

      const result = await runModel(mockAudioBuffer);
      expect(result).toBe('sample transcription');
    });

    it('should handle model inference errors', async () => {
      const mockAudioBuffer = [1, 2, 3];
      const mockSession = {
        run: jest.fn().mockRejectedValue(new Error('Inference failed'))
      };
      (InferenceSession.create as jest.Mock).mockResolvedValue(mockSession);

      await expect(runModel(mockAudioBuffer)).rejects.toThrow('Inference failed');
    });

    it('should handle empty audio buffer', async () => {
      const mockAudioBuffer: number[] = [];
      await expect(runModel(mockAudioBuffer)).rejects.toThrow();
    });
  });
});