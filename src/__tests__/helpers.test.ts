import { convertToPersianNumber } from '../utils/helpers';

describe('convertToPersianNumber', () => {
  test('should convert single digit numbers', () => {
    expect(convertToPersianNumber('1')).toBe('۱');
    expect(convertToPersianNumber('5')).toBe('۵');
    expect(convertToPersianNumber('0')).toBe('۰');
  });

  test('should convert multi-digit numbers', () => {
    expect(convertToPersianNumber('123')).toBe('۱۲۳');
    expect(convertToPersianNumber('9876')).toBe('۹۸۷۶');
  });

  test('should handle non-numeric characters', () => {
    expect(convertToPersianNumber('abc')).toBe('abc');
    expect(convertToPersianNumber('123abc')).toBe('۱۲۳abc');
    expect(convertToPersianNumber('abc123')).toBe('abc۱۲۳');
  });

  test('should handle empty string', () => {
    expect(convertToPersianNumber('')).toBe('');
  });

  test('should handle special characters and spaces', () => {
    expect(convertToPersianNumber('123 456')).toBe('۱۲۳ ۴۵۶');
    expect(convertToPersianNumber('1,234')).toBe('۱,۲۳۴');
  });
});