import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import InputComponent from '../components/InputComponent';

describe('InputComponent', () => {
  const mockOnSubmit = jest.fn();
  const mockOnTextChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render input field correctly', () => {
    const { getByPlaceholderText } = render(
      <InputComponent
        onSubmit={mockOnSubmit}
        onTextChange={mockOnTextChange}
        placeholder="Type a message"
      />
    );

    const input = getByPlaceholderText('Type a message');
    expect(input).toBeTruthy();
  });

  it('should call onTextChange when text changes', () => {
    const { getByPlaceholderText } = render(
      <InputComponent
        onSubmit={mockOnSubmit}
        onTextChange={mockOnTextChange}
        placeholder="Type a message"
      />
    );

    const input = getByPlaceholderText('Type a message');
    fireEvent.changeText(input, 'Hello');
    expect(mockOnTextChange).toHaveBeenCalledWith('Hello');
  });

  it('should call onSubmit when submit button is pressed', () => {
    const { getByTestId } = render(
      <InputComponent
        onSubmit={mockOnSubmit}
        onTextChange={mockOnTextChange}
        placeholder="Type a message"
      />
    );

    const submitButton = getByTestId('submit-button');
    fireEvent.press(submitButton);
    expect(mockOnSubmit).toHaveBeenCalled();
  });

  it('should clear input after submission', () => {
    const { getByPlaceholderText, getByTestId } = render(
      <InputComponent
        onSubmit={mockOnSubmit}
        onTextChange={mockOnTextChange}
        placeholder="Type a message"
      />
    );

    const input = getByPlaceholderText('Type a message');
    const submitButton = getByTestId('submit-button');

    fireEvent.changeText(input, 'Hello');
    fireEvent.press(submitButton);

    expect(input.props.value).toBe('');
  });
});