import { compareTexts, TextSegment } from '../utils/compareTexts';

describe('compareTexts', () => {
  test('should return empty array when both texts are empty', () => {
    const result = compareTexts('', '');
    expect(result).toEqual([]);
  });

  test('should identify matching text', () => {
    const result = compareTexts('hello world', 'hello world');
    expect(result).toEqual([{
      text: 'hello world',
      type: 'match'
    }]);
  });

  test('should identify added text', () => {
    const result = compareTexts('hello', 'hello world');
    expect(result).toEqual([
      { text: 'hello', type: 'match' },
      { text: ' world', type: 'add' }
    ]);
  });

  test('should identify removed text', () => {
    const result = compareTexts('hello world', 'hello');
    expect(result).toEqual([
      { text: 'hello', type: 'match' },
      { text: ' world', type: 'remove' }
    ]);
  });

  test('should handle complex text differences', () => {
    const result = compareTexts('hello beautiful world', 'hello amazing world');
    expect(result).toEqual([
      { text: 'hello ', type: 'match' },
      { text: 'beautiful', type: 'remove' },
      { text: 'amazing', type: 'add' },
      { text: ' world', type: 'match' }
    ]);
  });

  test('should handle special characters', () => {
    const result = compareTexts('hello! @world', 'hello! @earth');
    expect(result).toEqual([
      { text: 'hello! @', type: 'match' },
      { text: 'world', type: 'remove' },
      { text: 'earth', type: 'add' }
    ]);
  });
});