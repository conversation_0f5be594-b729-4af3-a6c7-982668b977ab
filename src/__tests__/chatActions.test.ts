import { addReqToHistory, addChatHistory, startNewChat, startTopic, submitChat, chatRequest, requestAgain } from '../components/chat/chatActions';
import store from '../store/store';

jest.mock('../../../store/store', () => ({
  dispatch: jest.fn(),
  getState: jest.fn()
}));

describe('Chat Actions', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('addReqToHistory', () => {
    it('should format and add request to history', () => {
      const text = 'Hello';
      const result = addReqToHistory(text);
      expect(result).toEqual({
        id: expect.any(String),
        text,
        type: 'user',
        timestamp: expect.any(Number)
      });
    });
  });

  describe('addChatHistory', () => {
    it('should format and add response to history', () => {
      const text = 'Response';
      const result = addChatHistory(text);
      expect(result).toEqual({
        id: expect.any(String),
        text,
        type: 'assistant',
        timestamp: expect.any(Number)
      });
    });
  });

  describe('startNewChat', () => {
    it('should initialize a new chat session', async () => {
      const mockDispatch = jest.fn();
      store.dispatch = mockDispatch;
      
      await startNewChat();
      expect(mockDispatch).toHaveBeenCalled();
    });
  });

  describe('submitChat', () => {
    it('should handle chat submission', () => {
      const text = 'Test message';
      const mockDispatch = jest.fn();
      store.dispatch = mockDispatch;

      submitChat(text);
      expect(mockDispatch).toHaveBeenCalledWith(expect.any(Object));
    });
  });
});