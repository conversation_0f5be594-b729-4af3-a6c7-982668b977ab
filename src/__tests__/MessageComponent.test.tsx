import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import MessageComponent from '../components/chat/MessageComponent';

describe('MessageComponent', () => {
  const mockMessage = {
    id: '1',
    text: 'Hello world',
    type: 'user',
    timestamp: Date.now()
  };

  it('should render message text correctly', () => {
    const { getByText } = render(
      <MessageComponent message={mockMessage} />
    );

    const messageText = getByText('Hello world');
    expect(messageText).toBeTruthy();
  });

  it('should apply correct style based on message type', () => {
    const { getByTestId } = render(
      <MessageComponent message={mockMessage} />
    );

    const messageContainer = getByTestId('message-container');
    expect(messageContainer.props.style).toMatchObject(
      expect.objectContaining({
        alignSelf: 'flex-end'
      })
    );
  });

  it('should render assistant message on the left', () => {
    const assistantMessage = {
      ...mockMessage,
      type: 'assistant'
    };

    const { getByTestId } = render(
      <MessageComponent message={assistantMessage} />
    );

    const messageContainer = getByTestId('message-container');
    expect(messageContainer.props.style).toMatchObject(
      expect.objectContaining({
        alignSelf: 'flex-start'
      })
    );
  });

  it('should handle long messages correctly', () => {
    const longMessage = {
      ...mockMessage,
      text: 'A'.repeat(500)
    };

    const { getByTestId } = render(
      <MessageComponent message={longMessage} />
    );

    const messageText = getByTestId('message-text');
    expect(messageText).toBeTruthy();
  });
});