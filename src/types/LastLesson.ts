interface AssessmentScores {
  Grammar?: number;
  Vocabulary?: number;
  Conversation?: number;
  average?: number;
}

interface AssessmentRewards {
  pointsEarned?: number;
  gemsEarned?: number;
  totalPoints?: number;
  totalGems?: number;
  gameLevel?: number;
  levelProgress?: number;
}

interface Assessment {
  id: string;
  scores: AssessmentScores;
  rewards: AssessmentRewards;
  createdAt: string;
}

interface Scenario {
  id: string;
  ـid: string;
  title: string;
  translated_title: string;
  ai_role: string;
  user_role: string;
  scenario: string;
  level: string;
  icon: string;
  icon_pack: string;
}

interface LastCompletedLessonData {
  assessment: Assessment;
  scenario: Scenario;
  chatId: string;
  messageCount: number;
}

interface StatBadgeProps {
  icon: React.ReactNode;
  value: number;
  color: string;
  bgColor: string;
}

interface ProgressCircleProps {
  percentage: number;
}

export type {
  LastCompletedLessonData,
  Assessment,
  AssessmentScores,
  AssessmentRewards,
  Scenario,
  StatBadgeProps,
  ProgressCircleProps,
};
