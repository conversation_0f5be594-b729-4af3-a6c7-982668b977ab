import {
  StyleSheet,
} from "react-native";

export default StyleSheet.create({
  container: {
    marginBottom: 10,
  },
  statNumbers: {
    marginRight: 8,
    marginLeft: 4,
    fontFamily: "EstedadBold",
    fontSize: 17,
  },
  topStatsContainer: {
    flexDirection: "row-reverse",
    justifyContent: "space-between",
    marginBottom: 15,
    alignItems: "center",
  },
  topStatsRow: {
    flexDirection: "row",
    justifyContent: "flex-end",
  },
  statBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginLeft: 8,
  },
  // Card styles
  dailyLessonCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    marginBottom: 10,
  },
  todayLabel: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 12,
    fontFamily: "EstedadRegular",
    marginBottom: 4,
  },
  dailyLessonTitle: {
    color: "white",
    fontSize: 22,
    fontFamily: "EstedadRegular",
  },
  cardContent: {
    marginTop: 10,
  },
  cardStatsRow: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginBottom: 15,
  },
  cardStatBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    marginLeft: 8,
  },
  cardStatNumbers: {
    marginLeft: 6,
    fontFamily: "EstedadBold",
    fontSize: 16,
    color: "white",
  },
  buttonsRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 10,
  },
  lessonButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 24,
  },
  buttonText: {
    color: "white",
    fontFamily: "EstedadRegular",
    fontSize: 16,
  },
  progressCircleContainer: {
    alignItems: "center",
    justifyContent: "center",
  },
  progressCircle: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: "#57e690",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  progressText: {
    color: "white",
    fontFamily: "EstedadBold",
    fontSize: 16,
  },
});