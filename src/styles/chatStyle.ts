import {
  StyleSheet,
} from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: "4%",
  },
  textInput: {
    width: "30%",
    height: 50,
    padding: 10,
    borderRadius: 50,
    backgroundColor: "#fff",
    marginBottom: 10,
  },
  icon: {
    width: 50,
    height: 50,
    marginRight: 10,
  },
  waveLoadingAnimation: {
    width: 50,
    height: 50,
    marginRight: 10,
  },
  inputContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: "90%",
    borderRadius: 40,
    borderWidth: 2,
    borderColor: "lightgrey",
    backgroundColor: "#fafcff",
    margin: 10,
    paddingHorizontal: 10,
    // height: 60,
  },
  input: {
    // height: 60,
    width: "90%",
    paddingLeft: "2%",
    fontSize: 17,
  },
  menu: {
    // padding: 10,
    width: 250,
  },
  menuItem: {
    padding: 10,
  },
  menuText: {
    fontSize: 16,
    color: "#333",
  },
  separator: {
    height: 1,
    backgroundColor: "#E0E0E0",
    marginVertical: 4,
  },
});
