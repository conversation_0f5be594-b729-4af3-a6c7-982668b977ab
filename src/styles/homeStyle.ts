import {
  StyleSheet,
} from "react-native";

export default StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F7FA",
    paddingTop: 60,
  },
  scrollContainer: {
    padding: 16,
  },
  statsContainer: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginBottom: 16,
  },
  statItem: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 16,
  },
  statText: {
    fontFamily: "EstedadRegular",
    fontSize: 16,
    marginLeft: 4,
    color: "#333",
  },
  classicLessonCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  practiceCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  cardHeader: {
    marginBottom: 10,
  },
  todayLabel: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 20,
    fontWeight: "500",
    marginBottom: 4,
    fontFamily: "EstedadRegular",
  },
  cardHeaderLabel: {
    color: "white",
    fontSize: 22,
    fontFamily: "EstedadRegular",
    fontWeight: "500",
  },
  iconContainer: {
    flexDirection: "row",
    marginTop: 8,
  },
  iconBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
    marginRight: 8,
  },
  iconText: {
    color: "white",
    marginLeft: 4,
    fontSize: 14,
  },
  buttonContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  lessonButton: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 16,
    paddingVertical: 8,
    paddingHorizontal: 24,
  },
  buttonText: {
    color: "white",
    fontWeight: "500",
    fontSize: 16,
  },
  progressCircle: {
    width: 48,
    height: 48,
    borderRadius: 22,
    backgroundColor: "#27b03e",
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  progressText: {
    color: "white",
    fontSize: 16,
    fontFamily: "EstedadBold",
  },

  practiceSubtitle: {
    color: "rgba(255, 255, 255, 0.9)",
    fontSize: 16,
    marginVertical: 12,
    fontFamily: "EstedadRegular",
    width: "80%",
    alignSelf: "flex-end",
  },
  talkButton: {
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 16,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignSelf: "flex-start",
    // marginTop: 10,
  },
  talkButtonText: {
    color: "white",
    fontWeight: "500",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
  nextLessonCard: {
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: "hidden",
  },
  nextLessonHeader: {
    marginBottom: 24,
  },
  nextLessonTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 4,
    fontFamily: "EstedadRegular",
  },
  timeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  timeText: {
    color: "#888",
    fontSize: 12,
    marginLeft: 4,
  },
  nextLessonFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  countdownText: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  unlockButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 123, 172, 0.1)",
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
  },
  unlockText: {
    color: "#FF7BAC",
    fontWeight: "500",
    fontSize: 14,
    marginLeft: 4,
  },
  dailyLessonHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 20,
  },
  // todayLabel: {
  //   color: "rgba(255, 255, 255, 0.8)",
  //   fontSize: 14,
  //   fontWeight: "500",
  //   fontFamily: "EstedadRegular",
  // },
  dailyLessonTitle: {
    color: "white",
    fontSize: 17,
    fontWeight: "500",
    flex: 1,
    marginLeft: 10,
    fontFamily: "EstedadRegular",
    textAlign: "right",
    marginRight: 7,
  },
  limitedBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    borderRadius: 20,
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.5)",
  },
  limitedText: {
    color: "white",
    marginLeft: 6,
    fontWeight: "600",
    fontSize: 12,
    fontFamily: "EstedadRegular",
  },
  progressBar: {
    height: 5,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    borderRadius: 4,
    marginVertical: 20,
    width: "90%",
    alignSelf: "center",
  },
  progressDot: {
    width: 16,
    height: 10,
    borderRadius: 8,
    backgroundColor: "#57e690",
    position: "absolute",
    left: 0,
    top: -3,
  },
  translatedTitle: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
    // marginBottom: 10,
    // lineHeight: 32,
    fontFamily: "EstedadRegular",
    textAlign: "right",
    marginRight: 7,
  },
  startLessonButton: {
    backgroundColor: "white",
    borderRadius: 30,
    paddingVertical: 14,
    paddingHorizontal: 24,
    alignSelf: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  startLessonText: {
    color: "#4A6FFF",
    fontWeight: "600",
    fontSize: 18,
    fontFamily: "EstedadRegular",
  },
});
