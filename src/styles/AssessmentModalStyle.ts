import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  container: {
    backgroundColor: "#F8F9FB",
    padding: 16,
    // borderRadius: 10,
    // elevation: 2,
    width: "100%",
  },
  modalBackground: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  alertContainer: {
    // width: 300,
    backgroundColor: "#fff",
    borderRadius: 10,
    alignItems: "center",
    height: "100%"
  },
  alertTitle: {
    fontSize: 20,
    marginBottom: 10,
    color: "green",
    fontFamily: "EstedadBold",
  },
  alertMessage: {
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
    color: "#666",
    fontFamily: "EstedadRegular",
  },
  alertButton: {
    backgroundColor: "#6200EE",
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 7,
    width: 200,
  },
  buttonText: {
    color: "#fff",
    fontSize: 16,
    fontFamily: "EstedadRegular",
    textAlign: "center",
  },
  scores: {
    marginTop: 10,
    marginBottom: 40,
    width: "100%",
    padding: 20,
  },
  text: {
    fontSize: 16,
    color: "#333",
    fontFamily: "EstedadRegular",
  },
  title: {
    fontSize: 20,
    fontFamily: "EstedadRegular",
    color: "#00A36C",
  },
  scoreItem: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: "#212529",
    fontFamily: "EstedadRegular",
  },
  progressContainer: {
    height: 8,
    backgroundColor: "#E0E0E0",
    borderRadius: 5,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#5A4FCF",
    borderRadius: 5,
  },
  score: {
    fontSize: 16,
    color: "#212529",
  },
  separator: {
    borderBottomWidth: 1,
    borderBottomColor: "#D3D3D3",
    marginVertical: 8,
  },
  totalScoreContainer: {
    flexDirection: "row-reverse",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalLabel: {
    fontSize: 16,
    fontFamily: "EstedadRegular",
    color: "#212529",
  },
  totalScore: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#5A4FCF",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    width: "100%",
    backgroundColor: "#3b82f6",
    height: 60,
    borderTopRightRadius: 10,
    borderTopLeftRadius: 10,
    paddingHorizontal: 12,
  },
  errorContainer: {
    backgroundColor: "#fff3cd",
    borderColor: "#ffeeba",
    borderWidth: 1,
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
  },
  errorText: {
    color: "#856404",
    fontFamily: "EstedadRegular",
    fontSize: 16,
    textAlign: "center",
  },
  rewardsContainer: {
    backgroundColor: "#F8F9FB",
    padding: 16,
    borderRadius: 10,
    elevation: 2,
    width: "100%",
    marginTop: 16,
  },
  rewardsTitle: {
    fontSize: 18,
    fontFamily: "EstedadRegular",
    color: "#212529",
    marginBottom: 12,
    textAlign: "center",
  },
  rewardItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 10,
  },
  rewardIconContainer: {
    width: 30,
    alignItems: "center",
  },
  rewardLabel: {
    flex: 1,
    fontSize: 14,
    fontFamily: "EstedadRegular",
    color: "#212529",
    marginRight: 10,
  },
  rewardValue: {
    fontSize: 16,
    fontFamily: "EstedadBold",
    color: "#4CAF50",
  },
  rewardTotalsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: 10,
  },
  rewardTotalItem: {
    flex: 1,
    alignItems: "center",
  },
  rewardTotalLabel: {
    fontSize: 12,
    fontFamily: "EstedadRegular",
    color: "#212529",
    marginTop: 4,
  },
  rewardTotalValue: {
    fontSize: 16,
    fontFamily: "EstedadBold",
    color: "#5A4FCF",
  },
  levelProgressContainer: {
    marginTop: 10,
  },
  levelProgressLabel: {
    fontSize: 14,
    fontFamily: "EstedadRegular",
    color: "#212529",
    marginBottom: 5,
  },
  levelProgressBarContainer: {
    height: 8,
    backgroundColor: "#E0E0E0",
    borderRadius: 5,
    overflow: "hidden",
    marginBottom: 5,
  },
  levelProgressBar: {
    height: "100%",
    backgroundColor: "#5A4FCF",
    borderRadius: 5,
  },
  levelProgressValue: {
    fontSize: 12,
    fontFamily: "EstedadRegular",
    color: "#212529",
    textAlign: "right",
  },
});

export default styles;
