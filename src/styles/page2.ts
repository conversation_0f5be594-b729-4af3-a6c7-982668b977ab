import {
  StyleSheet,
} from "react-native";

export default StyleSheet.create({
  rewardsContainer: {
    flex: 1,
    height: "100%",
    justifyContent: "space-between",
    paddingVertical: 80,
  },
  circularProgressContainer: {
    justifyContent: "center",
    alignItems: "center",
    // paddingBottom: 20,
  },
  progressTextOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
  },
  levelText: {
    color: "white",
    fontSize: 90,
    fontWeight: "bold",
    textAlign: "center",
    // marginBottom: -2,
  },
  completedText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "bold",
    textAlign: "center",
    marginTop: -2,
    marginBottom: -2,
    letterSpacing: 2,
  },
  progressText: {
    color: "#32DCF3",
    fontSize: 13,
    textAlign: "center",
    marginTop: 2,
    fontWeight: "bold",
  },
  statsRow: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 18,
  },
  statCard: {
    backgroundColor: "rgba(66, 66, 116, 0.6)",
    borderRadius: 16,
    alignItems: "center",
    justifyContent: "center",
    width: 130,
    height: 130,
    marginHorizontal: 8,
    // shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 4,
    // elevation: 3,
  },
  statValue: {
    color: "white",
    fontSize: 28,
    fontWeight: "bold",
    marginTop: 8,
  },
  statLabel: {
    color: "#BBBBBB",
    fontSize: 13,
    fontWeight: "bold",
    marginTop: 2,
  },
  xpIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#FF7F50",
    justifyContent: "center",
    alignItems: "center",
    marginTop: 8,
  },
  gemIconContainer: {
    width: 32,
    height: 32,
    backgroundColor: "#32DCF3",
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    marginTop: 8,
    transform: [{ rotate: "45deg" }],
  },
  motivationalText: {
    color: "#fff",
    fontSize: 23,
    textAlign: "center",
    fontFamily: "EstedadBold",
    letterSpacing: 2,
    // marginBottom: 30,
    paddingTop: 50,

  },
});