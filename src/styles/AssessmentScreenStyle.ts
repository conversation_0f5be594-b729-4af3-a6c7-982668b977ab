import { StyleSheet } from "react-native";

const styles = StyleSheet.create({
  screenContainer: {
    flex: 1,
    // backgroundColor: "#5A4FCF",
    height: "100%",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#5A4FCF",
    height: 60,
    paddingHorizontal: 16,
    elevation: 4,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  headerTitle: {
    color: "white",
    fontFamily: "EstedadRegular",
    fontSize: 20,
    textAlign: "center",
  },
  backButton: {
    padding: 8,
  },
  headerRight: {
    width: 28, // Same width as the back button icon for balance
  },
  scrollContainer: {
    flex: 1,
    backgroundColor: "#F5F7FA",
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#F5F7FA",
  },
  loadingText: {
    fontSize: 18,
    fontFamily: "EstedadRegular",
    color: "#5A4FCF",
  },
  container: {
    backgroundColor: "#009cad",
    padding: 16,
    borderRadius: 10,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    marginBottom: 16,
    paddingTop: 100,
    // width: "80%",
    paddingHorizontal: 30,
  },
  scoreItem: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    color: "#212529",
    fontFamily: "EstedadRegular",
  },
  progressContainer: {
    height: 8,
    backgroundColor: "#E0E0E0",
    borderRadius: 5,
    overflow: "hidden",
  },
  progressBar: {
    height: "100%",
    backgroundColor: "#5A4FCF",
    borderRadius: 5,
  },
  score: {
    fontSize: 16,
    color: "#212529",
    fontFamily: "EstedadRegular",
  },
  separator: {
    borderBottomWidth: 1,
    borderBottomColor: "#D3D3D3",
    marginVertical: 8,
  },
  totalScoreContainer: {
    flexDirection: "row-reverse",
    justifyContent: "space-between",
    alignItems: "center",
  },
  totalLabel: {
    fontSize: 16,
    fontFamily: "EstedadRegular",
    color: "#212529",
  },
  totalScore: {
    fontSize: 18,
    fontFamily: "EstedadBold",
    color: "#5A4FCF",
  },
  errorContainer: {
    backgroundColor: "#fff3cd",
    borderColor: "#ffeeba",
    borderWidth: 1,
    padding: 15,
    borderRadius: 5,
    marginBottom: 10,
  },
  errorText: {
    color: "#856404",
    fontFamily: "EstedadRegular",
    fontSize: 16,
    textAlign: "center",
  },
  rewardsContainer: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    marginBottom: 16,
    backgroundColor: "#5A4FCF",
    flex: 1,
    minHeight: "100%",
    paddingTop: 70,
  },
  rewardsTitle: {
    fontSize: 22,
    fontFamily: "EstedadBold",
    color: "#5A4FCF",
    marginBottom: 16,
    textAlign: "center",
  },
  rewardItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 14,
    backgroundColor: "#F8F9FB",
    padding: 12,
    borderRadius: 8,
  },
  rewardIconContainer: {
    width: 40,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F0F0F0",
    borderRadius: 20,
  },
  rewardLabel: {
    flex: 1,
    fontSize: 16,
    fontFamily: "EstedadRegular",
    color: "#212529",
    marginRight: 10,
  },
  rewardValue: {
    fontSize: 18,
    fontFamily: "EstedadBold",
    color: "#4CAF50",
  },
  rewardTotalsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginVertical: 16,
    padding: 16,
    borderRadius: 8,
  },
  rewardTotalItem: {
    flex: 1,
    alignItems: "center",
  },
  rewardTotalLabel: {
    fontSize: 14,
    fontFamily: "EstedadRegular",
    color: "#fff",
    marginTop: 6,
  },
  rewardTotalValue: {
    fontSize: 18,
    fontFamily: "EstedadBold",
    color: "#fff",
    marginTop: 2,
  },
  levelProgressContainer: {
    marginTop: 16,
    backgroundColor: "#F8F9FB",
    padding: 16,
    borderRadius: 8,
  },
  levelProgressLabel: {
    fontSize: 16,
    fontFamily: "EstedadRegular",
    color: "#212529",
    marginBottom: 8,
  },
  levelProgressBarContainer: {
    height: 10,
    backgroundColor: "#E0E0E0",
    borderRadius: 5,
    overflow: "hidden",
    marginBottom: 8,
  },
  levelProgressBar: {
    height: "100%",
    backgroundColor: "#5A4FCF",
    borderRadius: 5,
  },
  levelProgressValue: {
    fontSize: 14,
    fontFamily: "EstedadRegular",
    color: "#212529",
    textAlign: "right",
  },
  navigationButtonsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    padding: 16,
    backgroundColor: "#F5F7FA",
    borderTopWidth: 1,
    borderTopColor: "#E0E0E0",
  },
  navButton: {
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    minWidth: 120,
    alignItems: "center",
  },
  nextButton: {
    backgroundColor: "#5A4FCF",
  },
  prevButton: {
    backgroundColor: "#E0E0E0",
  },
  buttonText: {
    fontSize: 16,
    fontFamily: "EstedadRegular",
    color: "#FFFFFF",
    textAlign: "center",
  },
  prevButtonText: {
    color: "#333333",
  },
  textContainer: {
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
    marginTop: 35,
  },
  pointText: {
    fontSize: 20,
    color: "white",
    fontFamily: "EstedadRegular",
  },
  text: {
    fontSize: 35,
    fontWeight: "bold",
    color: "white",
  },
  CircularProgressContainer: {
    justifyContent: "center",
    alignItems: "center",
    flexDirection: "column",
    paddingTop: 50,
    paddingBottom: 40,
  },
  content: {
    alignItems: "center",
  },
  icon: {
    marginBottom: 15,
  },
  title: {
    fontSize: 24,
    fontFamily: "EstedadRegular",
    color: "#4CAF50",
    marginBottom: 10,
    textAlign: "center",
  },
  message: {
    fontSize: 16,
    fontFamily: "EstedadRegular",
    color: "#555",
    marginBottom: 20,
    textAlign: "center",
  },
  button: {
    backgroundColor: "#4CAF50",
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 7,
  },
  dotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    // marginTop: 20,
    // backgroundColor: '#fff',
    paddingHorizontal: 16,
    position: "absolute",
    top: 60,
    left: 0,
    right: 0,
    zIndex: 1,
  },
  storyBarContainer: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    gap: 7,
  },
  storyBarWrapper: {
    flex: 1,
    height: 5,
    backgroundColor: "#8a8888",
    borderRadius: 3,
    overflow: "hidden",
  },
  storyBarFill: {
    height: "100%",
    backgroundColor: "#fff",
    borderRadius: 3,
  },
  storyBarActive: {
    backgroundColor: "#fff",
  },
  storyBarCompleted: {
    backgroundColor: "#fff",
    width: "100%",
  },
  navigationIndicator: {
    position: "absolute",
    top: "50%",
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "rgba(255, 255, 255, 0.3)",
    justifyContent: "center",
    alignItems: "center",
    marginTop: -20,
  },
  leftIndicator: {
    left: 10,
  },
  rightIndicator: {
    right: 10,
  },
});

export default styles;
