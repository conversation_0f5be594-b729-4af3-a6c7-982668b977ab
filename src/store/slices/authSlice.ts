import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../store";

interface AuthState {
  isAuthenticated: boolean;
  token: string | null;
  googleId: string;
  name: string | null;
  email: string;
  photo: string | null;
  familyName: string | null;
  givenName: string | null;
  level: string | null;
  isPremium: boolean;
  remainingDays: number;
  coins: number;
}

interface GoogleSignInResponse {
  idToken: string;
  scopes: string[];
  serverAuthCode: null | string;
  user: {
    email: string;
    familyName: string;
    givenName: string;
    id: string;
    name: string;
    photo: string;
  };
  token: string;
  level: string;
  userData: any;
}

const initialState: AuthState = {
  isAuthenticated: false,
  token: null,
  googleId: "",
  name: "",
  email: "",
  photo: null,
  familyName: null,
  givenName: null,
  level: null,
  isPremium: false,
  remainingDays: 0,
  coins: 0,
};

export const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    login: (state, action: PayloadAction<GoogleSignInResponse>) => {
      state.isAuthenticated = true;
      state.token = action.payload.token;
      state.level = action.payload.level;

      state.email = action.payload.user.email;
      state.googleId = action.payload.user.id;
      state.name = action.payload.user.name;
      state.familyName = action.payload.user.familyName;
      state.givenName = action.payload.user.givenName;
      state.photo = action.payload.user.photo;
      state.isPremium = action.payload.userData.isPremium;
      state.remainingDays = action.payload.userData.remainingDays;
    },
    logout: (state) => {
      state.isAuthenticated = false;
      state.token = null;
      state.googleId = "";
      state.name = "";
      state.email = "";
      state.photo = null;
      state.familyName = null;
      state.givenName = null;
      state.level = null;
      state.isPremium = false;
      state.remainingDays = 0;
    },
    setLevel: (state, action) => {
      state.level = action.payload.level;
    },
    updatePremium: (state, action) => {
      state.isPremium = action.payload.isPremium;
      state.remainingDays = action.payload.remainingDays;
    },
    updateCoins: (state, action: PayloadAction<number>) => {
      state.coins = action.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const { login, logout, setLevel, updatePremium, updateCoins } =
  authSlice.actions;

export const selectAuth = (state: RootState) => state.auth;

export default authSlice.reducer;
