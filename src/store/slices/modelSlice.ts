import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import type { RootState } from "../store";
import { InferenceSession } from "onnxruntime-react-native";

interface ModelState {
  Session?: InferenceSession | null;
  newChat?: boolean;
  textOfSpeech?: string;
}

const initialState: ModelState = {
  Session: null,
  newChat: false,
  textOfSpeech: "",
};

export const modelSlice = createSlice({
  name: "model",
  initialState,
  reducers: {
    createSession: (state, action: PayloadAction<ModelState>) => {
      state.Session = action.payload.Session;
      // state.modelLoaded = action.payload.modelLoaded;
    },
    initNewChat: (state, action: PayloadAction<ModelState>) => {
      state.newChat = action.payload.newChat;
    },
    setTextOfSpeech: (state, action: PayloadAction<ModelState>) => {
      state.textOfSpeech = action.payload.textOfSpeech;
    },
  },
});

// Action creators are generated for each case reducer function
export const { createSession, initNewChat, setTextOfSpeech } = modelSlice.actions;

export const selectAuth = (state: RootState) => state.auth;

export default modelSlice.reducer;
