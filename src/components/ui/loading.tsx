import React, { useEffect } from "react";
import {
  StyleSheet,
  View,
  Modal,
  ActivityIndicator,
} from "react-native";
import { useAppSelector } from "../../store/hooks";

export default () => {
  const { loading } = useAppSelector((state) => state.loading);
  
  return (
    <Modal
      transparent={true}
      animationType={"none"}
      visible={loading}
      style={{ zIndex: 1100 }}
      onRequestClose={() => {}}
    >
      <View style={styles.modalBackground}>
        <View style={styles.activityIndicatorWrapper}>
          <ActivityIndicator animating={loading} size={24} color="#0000ff" />
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    alignItems: "center",
    flexDirection: "column",
    justifyContent: "space-around",
    backgroundColor: "#rgba(0, 0, 0, 0)",
    zIndex: 1000,
    height: 300,
    // marginVertical: "30%",
  },
  activityIndicatorWrapper: {
    backgroundColor: "#FFFFFF",
    height: 40,
    width: 40,
    borderRadius: 100,
    display: "flex",
    alignItems: "center",
    justifyContent: "space-around",
  },
});
