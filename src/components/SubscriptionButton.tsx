import React from "react";
import { StyleSheet, View, Text, Pressable, Animated } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { Feather, Ionicons } from "@expo/vector-icons";
import { useNavigation } from "@react-navigation/native";

const SubscriptionButton = () => {
  // Animation values
  const scaleAnim = React.useRef(new Animated.Value(1)).current;
  const circleScale = React.useRef(new Animated.Value(1)).current;
  const overlayOpacity = React.useRef(new Animated.Value(1)).current;
  const navigation = useNavigation();

  // Handle press animations
  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1.05,
        useNativeDriver: true,
      }),
      Animated.timing(overlayOpacity, {
        toValue: 0,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(circleScale, {
        toValue: 1.5,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleAnim, {
        toValue: 1,
        useNativeDriver: true,
      }),
      Animated.timing(overlayOpacity, {
        toValue: 1,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.spring(circleScale, {
        toValue: 1,
        useNativeDriver: true,
      }),
    ]).start();
  };

  return (
    <Pressable
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={styles.buttonContainer}
      onPress={() => navigation.navigate("subscription")}
    >
      <LinearGradient
        colors={["#FBBF24", "#FCD34D", "#FBBF24"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 0 }}
        style={styles.gradient}
      >
        <Animated.View
          style={[
            styles.button,
            {
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* Overlay */}
          <Animated.View
            style={[styles.overlay, { opacity: overlayOpacity }]}
          />

          {/* Content */}
          <View style={styles.contentContainer}>
            <Ionicons name="sparkles-outline" size={24} color="#4C1D95" />
            <View style={styles.textContainer}>
              <Text style={styles.title}>خرید اشتراک</Text>
              <Text style={styles.subtitle}>
                دسترسی نامحدود به تمام امکانات
              </Text>
            </View>
          </View>

          {/* Decorative circles */}
          <Animated.View
            style={[
              styles.circle,
              styles.topCircle,
              { transform: [{ scale: circleScale }] },
            ]}
          />
          <Animated.View
            style={[
              styles.circle,
              styles.bottomCircle,
              { transform: [{ scale: circleScale }] },
            ]}
          />
        </Animated.View>
      </LinearGradient>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  buttonContainer: {
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 5,
    elevation: 5,
    marginTop: 60,
    marginHorizontal: "auto",
  },
  gradient: {
    borderRadius: 12,
    overflow: "hidden",
  },
  button: {
    width: 280,
    paddingVertical: 13,
    paddingHorizontal: 20,
    position: "relative", // This is needed for absolute positioning of circles
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  contentContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  textContainer: {
    alignItems: "flex-end",
  },
  title: {
    fontSize: 20,
    color: "#4C1D95",
    textAlign: "right",
    fontFamily: "EstedadRegular",
  },
  subtitle: {
    fontSize: 14,
    color: "#4C1D95",
    marginTop: 4,
    textAlign: "right",
    fontFamily: "EstedadRegular",
  },
  circle: {
    position: "absolute", // This is crucial for circle positioning
    width: 96, // Required for circle size
    height: 96, // Required for circle size
    borderRadius: 48,
    backgroundColor: "#FEF08A",
    opacity: 0.2,
  },
  topCircle: {
    top: -20,
    right: -20,
  },
  bottomCircle: {
    bottom: -20,
    left: -20,
  },
});

export default SubscriptionButton;
