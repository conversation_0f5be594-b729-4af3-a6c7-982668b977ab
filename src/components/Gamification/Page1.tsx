import React, { useRef, useEffect } from "react";
import { View, Animated, ImageBackground, StyleSheet, Dimensions, Easing } from "react-native";
import { useFocusEffect } from '@react-navigation/native';

const { width, height } = Dimensions.get('window');

const RewardsCard = ({}) => {
  // Create animated value for the star's position
  const starPosition = useRef(new Animated.Value(height)).current;

  // Function to start the animation
  const animateStar = () => {
    // Reset the star position to below the screen
    starPosition.setValue(height);

    // Animate the star rising from below
    Animated.timing(starPosition, {
      toValue: height * 0.06, // Final position (adjust as needed)
      duration: 2000, // Animation duration in ms
      easing: Easing.elastic(1), // Add a bounce effect
      useNativeDriver: true, // Better performance
    }).start();
  };

  // Start animation when component mounts
  useEffect(() => {
    animateStar();
  }, []);

  // Restart animation when screen comes into focus
  useFocusEffect(
    React.useCallback(() => {
      animateStar();
      return () => {}; // Cleanup function
    }, [])
  );

  return (
    <ImageBackground
      source={require('../../../assets/gamification/congratulation-background.jpg')}
      style={localStyles.backgroundImage}
    >
      {/* <View style={localStyles.container}> */}
        <Animated.Image
          source={require('../../../assets/gamification/congratulation-start.png')}
          style={[
            localStyles.congratulationImage,
            {
              transform: [{ translateY: starPosition }]
            }
          ]}
          resizeMode="contain"
        />
      {/* </View> */}
    </ImageBackground>
  );
};

const localStyles = StyleSheet.create({
  backgroundImage: {
    flex: 1,
    width: '100%',
    height: '100%',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
  container: {
    flex: 1,
    
  },
  congratulationImage: {
    // width: width,
    height: height  ,
    // position: 'absolute', // Position absolutely to allow transform
    // bottom: -80, // Base position from bottom
  }
});

export default RewardsCard;
