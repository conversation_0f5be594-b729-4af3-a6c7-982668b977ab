import { View, Animated, TouchableOpacity } from "react-native";
import styles from "../../styles/AssessmentScreenStyle";
import { useEffect, useState } from "react";

interface StoryProps {
  barAnimations: Animated.Value[];
  page: number;
  setPage: (page: number) => void;
  setBarAnimations: (animations: Animated.Value[]) => void;
  isPaused: boolean;
}

const Story: React.FC<StoryProps> = ({
  barAnimations,
  page,
  setPage,
  setBarAnimations,
  isPaused,
}) => {
  const [autoAdvanceTimer, setAutoAdvanceTimer] =
    useState<NodeJS.Timeout | null>(null);

  // Set animation duration based on current page
  const getAnimationDuration = () => {
    if(page === 0) return 4000;
    return page === 1 ? 7000 : 5000; // 7 seconds for Page2, 5 seconds for other pages
  };

  // Initialize bar animations when component mounts
  useEffect(() => {
    const animations = [0, 1, 2].map(() => new Animated.Value(0));
    setBarAnimations(animations);
  }, []);

  // Add effect to resume bar animation when isPaused becomes false
  useEffect(() => {
    if (!isPaused && barAnimations.length === 4) {
      // Only animate the current page's bar if not already full
      const anim = barAnimations[page];
      if (anim) {
        // Get the current value - using a listener since __getValue is not available in the type
        let currentValue = 0;
        const valueListener = anim.addListener(({ value }) => {
          currentValue = value;
          anim.removeListener(valueListener);
        });

        if (currentValue < 1) {
          Animated.timing(anim, {
            toValue: 1,
            duration: getAnimationDuration(),
            useNativeDriver: false,
          }).start(({ finished }) => {
            if (finished && page < 2 && !isPaused) {
              setPage(page + 1);
            }
          });
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isPaused, page, barAnimations]);

  // Handle page changes and animations
  useEffect(() => {
    // Clear any existing timer
    if (autoAdvanceTimer) {
      clearTimeout(autoAdvanceTimer);
      setAutoAdvanceTimer(null);
    }

    // Reset all animations
    barAnimations.forEach((anim: Animated.Value, index: number) => {
      if (index < page) {
        // Set previous bars to completed (100%)
        anim.setValue(1);
      } else if (index > page) {
        // Reset upcoming bars
        anim.setValue(0);
      } else {
        // Animate current bar
        anim.setValue(0);

        // Only start animation if not paused
        if (!isPaused) {
          Animated.timing(anim, {
            toValue: 1,
            duration: getAnimationDuration(),
            useNativeDriver: false,
          }).start(({ finished }) => {
            if (finished && page < 2 && !isPaused) {
              // Auto-advance to next page when animation completes
              setPage(page + 1);
            }
          });
        }
      }
    });
  }, [
    page,
    barAnimations,
  ]);

  return (
    <View style={styles.dotsContainer}>
      <View style={styles.storyBarContainer}>
        {[0, 1, 2].map((index) => (
          <TouchableOpacity
            key={index}
            onPress={() => setPage(index)}
            style={styles.storyBarWrapper}
            activeOpacity={0.7}
          >
            {barAnimations[index] && (
              <Animated.View
                style={[
                  styles.storyBarFill,
                  {
                    width: barAnimations[index].interpolate({
                      inputRange: [0, 1],
                      outputRange: ["0%", "100%"],
                    }),
                  },
                  index < page && styles.storyBarCompleted,
                ]}
              />
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );
};

export default Story;
