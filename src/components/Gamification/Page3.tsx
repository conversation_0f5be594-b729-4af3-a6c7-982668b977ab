import React, { useRef, useEffect } from "react";
import { View, Text, Animated, StyleSheet, TouchableOpacity, Pressable } from "react-native";
import { useIsFocused, useNavigation } from "@react-navigation/native";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import styles from "../../styles/AssessmentScreenStyle";

// Define colorful bar colors
const BAR_COLORS = ["#FF6B6B", "#4ECDC4", "#FFD166"];

// Define types for scores
interface Scores {
  Grammar?: number;
  Vocabulary?: number;
  Conversation?: number;
  average?: number;
}

const ScoreCard = ({ scores }: { scores: Scores | null }) => {
  const isFocused = useIsFocused();
  const navigation = useNavigation<any>();

  const goToHomeScreen = () => {
    navigation.navigate("homeScreen", { refreshGamification: true });
  };

  return (
    <LinearGradient
      colors={["#667EEA", "#764BA2"]} // Purple gradient similar to the login screen
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={localStyles.gradientContainer}
    >
      {/* Close button */}
      <Pressable
        style={localStyles.closeButton}
        onPress={()=>goToHomeScreen()}
      >
        <Ionicons name="close" size={24} color="#FFFFFF" />
      </Pressable>

      <Text style={[localStyles.motivationalText, { fontSize: 21 }]}>
        نمره هایی که از این درس بدست آوردی
      </Text>

      <View style={localStyles.scoreCardContainer}>
        <ScoreItem
          label="گرامر"
          score={scores?.Grammar}
          colorIndex={0}
          isFocused={isFocused}
        />
        <ScoreItem
          label="واژگان"
          score={scores?.Vocabulary}
          colorIndex={1}
          isFocused={isFocused}
        />
        <ScoreItem
          label="مکالمه"
          score={scores?.Conversation}
          colorIndex={2}
          isFocused={isFocused}
        />
        <View style={styles.separator} />
        <View style={styles.totalScoreContainer}>
          <Text style={styles.totalLabel}>درصد کل</Text>
          <Text style={styles.totalScore}>{scores?.average}%</Text>
        </View>
      </View>
    </LinearGradient>
  );
};

const ScoreItem = ({
  label,
  score,
  colorIndex,
  isFocused,
}: {
  label: string;
  score?: number;
  colorIndex: number;
  isFocused: boolean;
}) => {
  const animatedWidth = useRef(new Animated.Value(0)).current;

  // Animate the progress bar when the page is focused
  useEffect(() => {
    if (isFocused) {
      // Reset animation
      animatedWidth.setValue(0);

      // Start animation
      Animated.timing(animatedWidth, {
        toValue: score || 0,
        duration: 2000,
        useNativeDriver: false,
      }).start();
    }
  }, [isFocused, score]);

  return (
    <View style={styles.scoreItem}>
      <View
        style={{
          flexDirection: "row",
          width: "100%",
          justifyContent: "space-between",
          marginBottom: 5,
        }}
      >
        <Text style={styles.score}>{score}%</Text>
        <Text style={styles.label}>{label}</Text>
      </View>
      <View style={styles.progressContainer}>
        <Animated.View
          style={[
            styles.progressBar,
            {
              width: animatedWidth.interpolate({
                inputRange: [0, 100],
                outputRange: ["0%", "100%"],
              }),
              backgroundColor: BAR_COLORS[colorIndex],
            },
          ]}
        />
      </View>
    </View>
  );
};

// Local styles for this component
const localStyles = StyleSheet.create({
  gradientContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 0,
  },
  centeredContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingTop: 0, // Override the paddingTop from the original styles
    backgroundColor: "transparent", // Make sure the container is transparent to show the gradient
  },
  closeButton: {
    position: "absolute",
    top: 90,
    right: 20,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 10000000,
  },
  scoreCardContainer: {
    width: "90%",
    maxWidth: 400,
    backgroundColor: "#FFFFFF",
    borderRadius: 10,
    padding: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  motivationalText: {
    color: "#fff",
    fontSize: 23,
    textAlign: "center",
    fontFamily: "EstedadBold",
    letterSpacing: 2,
    marginBottom: 30,
  },
});

export default ScoreCard;
