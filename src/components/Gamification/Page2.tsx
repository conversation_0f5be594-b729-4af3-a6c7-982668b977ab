import React, { useRef, useState, useEffect } from "react";
import { View, Text, Image, Animated, StyleSheet, Easing } from "react-native";
import { MaterialIcons, Ionicons } from "@expo/vector-icons";
import Svg, { Circle, LinearGradient, Stop, Defs } from "react-native-svg";
import { useFocusEffect } from "@react-navigation/native";
import { LinearGradient as ExpoLinearGradient } from "expo-linear-gradient";
import localStyles from "../../styles/page2";

interface RewardsCardProps {
  rewards: {
    pointsEarned: number;
    gemsEarned: number;
    totalPoints: number;
    totalGems: number;
    gameLevel: number;
    levelProgress: number;
  };
}

interface CircularProgressProps {
  size: number;
  strokeWidth: number;
  duration?: number;
  level: string | number;
  levelProgress: number;
  nextLevel?: string | number;
}

interface AnimatedNumberProps {
  value: number;
  style?: any;
  duration?: number;
  formatValue?: (value: number) => string;
}

const AnimatedNumber: React.FC<AnimatedNumberProps> = ({
  value,
  style,
  duration = 2000,
  formatValue = (val) => Math.floor(val).toString(),
}) => {
  const animatedValue = useRef(new Animated.Value(0)).current;
  const [displayValue, setDisplayValue] = useState(formatValue(0));
  const previousValue = useRef(value);
  const listenerRef = useRef<string | null>(null);

  // Set up the animation listener once
  useEffect(() => {
    // Set up listener for animation
    const listener = animatedValue.addListener(({ value: val }) => {
      setDisplayValue(formatValue(val));
    });

    listenerRef.current = listener;

    // Clean up on unmount
    return () => {
      if (listenerRef.current) {
        animatedValue.removeListener(listenerRef.current);
      }
    };
  }, [animatedValue, formatValue]);

  // Use useFocusEffect to animate when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      // Reset animation when screen is focused
      animatedValue.setValue(0);

      // Start animation
      setTimeout(() => {
        Animated.timing(animatedValue, {
          toValue: value,
          duration: duration,
          useNativeDriver: false,
          easing: Easing.out(Easing.ease),
        }).start();
      }, 2500);

      // Update previous value
      previousValue.current = value;

      // Clean up when screen loses focus
      return () => {
        // Stop any ongoing animation
        animatedValue.stopAnimation();
      };
    }, [value, duration]) // Only re-run if value or duration changes
  );

  return (
    <View style={{ flexDirection: "row", alignItems: "center" }}>
      <Text style={[style, { marginRight: 3 }]}>+</Text>
      <Text style={style}>{displayValue}</Text>
    </View>
  );
};

const CircularProgress: React.FC<CircularProgressProps> = ({
  size,
  strokeWidth,
  duration,
  level,
  levelProgress,
  nextLevel,
}) => {
  const AnimatedCircle = Animated.createAnimatedComponent(Circle);
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;
  const animatedValue = useRef(new Animated.Value(0)).current;

  // Decorative ring radii and stroke widths
  const outerRingRadius = radius + 7; // 7px outside main
  const innerRingRadius = radius - 7; // 7px inside main
  const ringStrokeWidth = 2;

  useFocusEffect(() => {
    animatedValue.setValue(0);
    Animated.timing(animatedValue, {
      toValue: levelProgress, // Progress percentage (0 to 100)
      duration,
      useNativeDriver: true,
    }).start();
  });

  const strokeDashoffset = animatedValue.interpolate({
    inputRange: [0, 100],
    outputRange: [circumference, 0],
  });

  return (
    <View style={localStyles.circularProgressContainer}>
      <Svg width={size + 28} height={size + 28}>
        <Defs>
          <LinearGradient
            id="progressGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="0%"
          >
            <Stop offset="0%" stopColor="#2325ad" />
            <Stop offset="100%" stopColor="#32DCF3" />
          </LinearGradient>
        </Defs>
        {/* Inner Decorative Ring */}
        {/* <AnimatedCircle
          stroke="url(#innerGradient)"
          // stroke="#fff6"
          cx={(size + 28) / 2}
          cy={(size + 28) / 2}
          r={innerRingRadius}
          strokeWidth={strokeWidth - 3}
          fill="none"
          strokeDasharray={`${circumference} ${circumference}`}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${(size + 28) / 2} ${(size + 28) / 2})`}
        /> */}

        {/* Background Circle */}
        <Circle
          stroke="#eaecf3"
          cx={(size + 28) / 2}
          cy={(size + 28) / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          opacity={1}
        />
        {/* Progress Circle */}
        <AnimatedCircle
          stroke="url(#progressGradient)"
          cx={(size + 28) / 2}
          cy={(size + 28) / 2}
          r={radius}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={`${circumference} ${circumference}`}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          transform={`rotate(-90 ${(size + 28) / 2} ${(size + 28) / 2})`}
        />
      </Svg>
      {/* Overlayed Text */}
      <View style={localStyles.progressTextOverlay} pointerEvents="none">
        <Text style={localStyles.levelText}>{level}</Text>
        <Text style={localStyles.completedText}>LEVEL</Text>
        {/* <Text style={localStyles.progressText}>{levelProgress}% to Level {nextLevel || Number(level) + 1}</Text> */}
      </View>
    </View>
  );
};

const RewardsCard: React.FC<RewardsCardProps> = ({ rewards }) => {
  if (!rewards) return null;
  const { gameLevel, levelProgress, pointsEarned, gemsEarned } = rewards;
  const [motivationalText, setMotivationalText] = useState<string>(
    "عالیه! همینجوری ادامه بده"
  );
  const statCardOpacity = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Show stat cards after 1.5 seconds with fade-in effect
    setTimeout(() => {
      Animated.timing(statCardOpacity, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }, 1500);
  }, []);

  const AnimatedStatCard = Animated.createAnimatedComponent(View);

  return (
    <ExpoLinearGradient
      colors={["#185a9d", "#146ee3", "#185a9d"]} // light blue to dark blue
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={localStyles.rewardsContainer}
    >
      <Text style={localStyles.motivationalText}>{motivationalText}</Text>

      <CircularProgress
        size={250}
        strokeWidth={12}
        level={gameLevel}
        levelProgress={levelProgress}
        duration={2000}
      />
      <View style={localStyles.statsRow}>
        <AnimatedStatCard
          style={[localStyles.statCard, { opacity: statCardOpacity }]}
        >
          {/* <View style={localStyles.xpIconContainer}>
            <MaterialIcons name="add" size={22} color="#fff" />
          </View> */}
          <Image
            source={require("../../../assets/icons/star.png")}
            style={{ width: 42, height: 42, marginTop: 8 }}
          />
          <AnimatedNumber
            value={pointsEarned}
            style={localStyles.statValue}
            duration={2000}
          />
          {/* <Text style={localStyles.statLabel}>XP</Text> */}
        </AnimatedStatCard>
        <AnimatedStatCard
          style={[localStyles.statCard, { opacity: statCardOpacity }]}
        >
          {/* <View style={localStyles.gemIconContainer}>
            <Ionicons name="diamond" size={22} color="#fff" style={{ transform: [{ rotate: "-45deg" }] }} />
          </View> */}
          <Image
            source={require("../../../assets/icons/gem.png")}
            style={{ width: 42, height: 42, marginTop: 8 }}
          />
          <AnimatedNumber
            value={gemsEarned}
            style={localStyles.statValue}
            duration={2500}
          />
          {/* <Text style={localStyles.statLabel}>GEMS</Text> */}
        </AnimatedStatCard>
      </View>
    </ExpoLinearGradient>
  );
};

export default RewardsCard;
