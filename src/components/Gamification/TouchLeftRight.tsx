import { View, Text, Image, Animated, TouchableOpacity } from "react-native";
import styles from "../../styles/AssessmentScreenStyle";
import { Ionicons } from "@expo/vector-icons";
import { useEffect, useRef } from "react";

const TouchLeftRight = ({
  leftIndicatorScale,
  leftIndicatorOpacity,
  rightIndicatorScale,
  rightIndicatorOpacity,
  page,
  setPage,
  setIsPaused,
  barAnimations,
}) => {
  // Remove indicator animation on mount/page change
  // Only animate indicators on touch

  return (
    <View
      style={{
        position: "absolute",
        width: "100%",
        height: "100%",
        flexDirection: "row",
        zIndex: 1, // Reduced zIndex to be below the close button
        top: 120, // Add top margin to avoid interfering with the close button
      }}
    >
      {/* Left touch area - go to previous page */}
      <TouchableOpacity
        style={{ width: "30%", height: "100%" }}
        activeOpacity={1}
        onPress={() => {
          if (page > 0) {
            // Animate indicator when pressed
            Animated.sequence([
              Animated.timing(leftIndicatorScale, {
                toValue: 1.3,
                duration: 100,
                useNativeDriver: true,
              }),
              Animated.timing(leftIndicatorScale, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
              }),
            ]).start();

            // Show indicator
            Animated.sequence([
              Animated.timing(leftIndicatorOpacity, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
              }),
              Animated.delay(1000),
              Animated.timing(leftIndicatorOpacity, {
                toValue: 0,
                duration: 500,
                useNativeDriver: true,
              }),
            ]).start();

            setPage(page - 1);
          }
        }}
      >
        <Animated.View
          style={[
            styles.navigationIndicator,
            styles.leftIndicator,
            {
              opacity: leftIndicatorOpacity,
              transform: [{ scale: leftIndicatorScale }],
            },
          ]}
        >
          <Ionicons name="chevron-back" size={24} color="white" />
        </Animated.View>
      </TouchableOpacity>

      {/* Middle area - no action */}
      <View style={{ width: "40%", height: "100%" }} />

      {/* Right touch area - go to next page (disabled on page 3) */}
      <TouchableOpacity
        style={{ width: "30%", height: "100%" }}
        activeOpacity={1}
        disabled={page === 2}
        onPress={() => {
          // Only allow navigation to pages 0, 1, and 2
          if (page < 2) {
            // Animate indicator when pressed
            Animated.sequence([
              Animated.timing(rightIndicatorScale, {
                toValue: 1.3,
                duration: 100,
                useNativeDriver: true,
              }),
              Animated.timing(rightIndicatorScale, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
              }),
            ]).start();

            // Show indicator
            Animated.sequence([
              Animated.timing(rightIndicatorOpacity, {
                toValue: 1,
                duration: 100,
                useNativeDriver: true,
              }),
              Animated.delay(1000),
              Animated.timing(rightIndicatorOpacity, {
                toValue: 0,
                duration: 500,
                useNativeDriver: true,
              }),
            ]).start();

            if (barAnimations && barAnimations[page]) {
              barAnimations[page].setValue(1);
            }
            setPage(page + 1);
          }
        }}
      >
        <Animated.View
          style={[
            styles.navigationIndicator,
            styles.rightIndicator,
            {
              opacity: rightIndicatorOpacity,
              transform: [{ scale: rightIndicatorScale }],
            },
          ]}
        >
          <Ionicons name="chevron-forward" size={24} color="white" />
        </Animated.View>
      </TouchableOpacity>
    </View>
  );
};

export default TouchLeftRight;
