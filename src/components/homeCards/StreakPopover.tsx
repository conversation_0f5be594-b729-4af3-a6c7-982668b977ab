import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Popover from 'react-native-popover-view';
import { FontAwesome5 } from '@expo/vector-icons';
import { convertToPersianNumber } from '../../utils/helpers';

interface StreakStatus {
  saturday: boolean;
  sunday: boolean;
  monday: boolean;
  tuesday: boolean;
  wednesday: boolean;
  thursday: boolean;
  friday: boolean;
}

interface StreakPopoverProps {
  isVisible: boolean;
  onClose: () => void;
  streakCount: number;
  streakStatus: StreakStatus;
  fromView: React.RefObject<View>;
}

const DAYS_OF_WEEK = [
  { key: 'saturday', label: 'ش' },
  { key: 'sunday', label: 'ی' },
  { key: 'monday', label: 'د' },
  { key: 'tuesday', label: 'س' },
  { key: 'wednesday', label: 'چ' },
  { key: 'thursday', label: 'پ' },
  { key: 'friday', label: 'ج' },
];

const StreakPopover: React.FC<StreakPopoverProps> = ({
  isVisible,
  onClose,
  streakCount,
  streakStatus,
  fromView,
}) => {
  // We'll use the streakStatus object to determine which days are completed

  const [delayedVisible, setDelayedVisible] = useState(false);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (isVisible && fromView.current) {
      timeout = setTimeout(() => setDelayedVisible(true), 50);
    } else {
      setDelayedVisible(false);
    }
    return () => clearTimeout(timeout);
  }, [isVisible, fromView.current]);

  return (
    <>
      {fromView.current && (
        <Popover
          isVisible={delayedVisible}
          from={fromView}
          onRequestClose={onClose}
          popoverStyle={styles.popover}
        >
          <View style={styles.container}>
            <View style={styles.streakIconContainer}>
              <FontAwesome5 name="fire-alt" size={50} color="#ff3b30" />
            </View>
            <Text style={styles.streakText}>
              <Text style={styles.streakNumber}>
                {convertToPersianNumber(streakCount.toString())}
              </Text>
              <Text style={{marginLeft: 10}}>
                روز متوالی!
              </Text>
            </Text>
            <View style={styles.daysContainer}>
              {Object.keys(streakStatus).map((day, index) => (
                <View key={day} style={styles.dayColumn}>
                  <Text style={styles.dayLabel}>{DAYS_OF_WEEK[index].label}</Text>
                  <View
                    style={[
                      styles.dayCircle,
                      streakStatus[day as keyof StreakStatus] ? styles.completedDay : styles.incompleteDay
                    ]}
                  >
                    {streakStatus[day as keyof StreakStatus] && (
                      <FontAwesome5 name="check" size={14} color="#fff" />
                    )}
                  </View>
                </View>
              ))}
            </View>
            <View style={styles.explanationContainer}>
              <Text style={styles.explanationText}>
                <Text style={styles.highlightText}>استریک</Text> تعداد روزهایی است که
                به صورت متوالی تمرین کرده‌اید
              </Text>
            </View>
          </View>
        </Popover>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  popover: {
    borderRadius: 16,
    padding: 0,
    width: Dimensions.get('window').width * 0.85,
    maxWidth: 400,
  },
  container: {
    padding: 20,
    alignItems: 'center',
  },
  streakIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 5,
  },
  streakNumber: {
    fontFamily: 'EstedadBold',
    fontSize: 28,
    paddingRight: 10,
    marginHorizontal: 10,
    // color: '#fff',
  },
  streakText: {
    fontFamily: 'EstedadRegular',
    fontSize: 20,
    color: '#ff3b30',
    marginBottom: 20,
  },
  daysContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 20,
    paddingHorizontal: 10,
  },
  dayColumn: {
    alignItems: 'center',
  },
  dayLabel: {
    fontFamily: 'EstedadRegular',
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  dayCircle: {
    width: 30,
    height: 30,
    borderRadius: 15,
    alignItems: 'center',
    justifyContent: 'center',
  },
  completedDay: {
    backgroundColor: '#ff3b30',
  },
  incompleteDay: {
    backgroundColor: '#e0e0e0',
  },
  explanationContainer: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 15,
    width: '100%',
  },
  explanationText: {
    fontFamily: 'EstedadRegular',
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  highlightText: {
    color: '#ff3b30',
    fontFamily: 'EstedadBold',
  },
});

export default StreakPopover;
