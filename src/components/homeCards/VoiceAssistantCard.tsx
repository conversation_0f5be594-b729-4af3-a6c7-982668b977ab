import React from 'react';
import { View, Text, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useNavigation } from '@react-navigation/native';
import styles from '../../styles/homeStyle';

const VoiceAssistantCard = () => {
  const navigation = useNavigation();

  return (
    <LinearGradient
      colors={["#8B4513", "#D2691E"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.classicLessonCard}
    >
      <View style={[styles.cardHeader]}>
        <Text style={styles.cardHeaderLabel}>دستیار صوتی</Text>
        <Text style={styles.practiceSubtitle}>
          با دستیار هوشمند صحبت کن
        </Text>
      </View>

      <TouchableOpacity
        style={styles.talkButton}
        onPress={() => navigation.navigate("freeTalk", { showVoiceAssistant: true })}
      >
        <Text style={styles.talkButtonText}>شروع مکالمه</Text>
      </TouchableOpacity>
    </LinearGradient>
  );
};

export default VoiceAssistantCard;