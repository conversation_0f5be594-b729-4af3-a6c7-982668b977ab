import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Dimensions } from 'react-native';
import Popover from 'react-native-popover-view';
import { MaterialIcons } from '@expo/vector-icons';

interface StarPopoverProps {
  isVisible: boolean;
  onClose: () => void;
  pointsCount: number;
  fromView: React.RefObject<View>;
}

const StarPopover: React.FC<StarPopoverProps> = ({
  isVisible,
  onClose,
  pointsCount,
  fromView,
}) => {
  const [delayedVisible, setDelayedVisible] = useState(false);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (isVisible && fromView.current) {
      timeout = setTimeout(() => setDelayedVisible(true), 50);
    } else {
      setDelayedVisible(false);
    }
    return () => clearTimeout(timeout);
  }, [isVisible, fromView.current]);

  return (
    <>
      {fromView.current && (
        <Popover
          isVisible={delayedVisible}
          from={fromView}
          onRequestClose={onClose}
          popoverStyle={styles.popover}
        >
          <View style={styles.container}>
            <View style={styles.starIconContainer}>
              <MaterialIcons name="star" size={50} color="#FFD700" />
            </View>
            <Text style={styles.titleText}>امتیاز</Text>
            
            <View style={styles.explanationContainer}>
              <Text style={styles.explanationText}>
                <Text style={styles.highlightText}>امتیاز</Text> برای شرکت در لیگ‌های آینده استفاده می‌شوند
              </Text>
              <Text style={styles.explanationText}>
                هر چقدر <Text style={styles.highlightText}>امتیاز بیشتری</Text> داشته باشید، به مراحل بالاتر می‌روید
              </Text>
              <Text style={styles.explanationText}>
                با تکمیل تمرین‌های روزانه <Text style={styles.highlightText}>امتیاز بیشتری</Text> کسب کنید
              </Text>
            </View>
          </View>
        </Popover>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  popover: {
    borderRadius: 16,
    padding: 0,
    width: Dimensions.get('window').width * 0.85,
    maxWidth: 400,
  },
  container: {
    padding: 20,
    alignItems: 'center',
  },
  starIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 5,
  },
  titleText: {
    fontFamily: 'EstedadBold',
    fontSize: 24,
    color: '#aaad00',
    marginBottom: 20,
  },
  explanationContainer: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 15,
    width: '100%',
  },
  explanationText: {
    fontFamily: 'EstedadRegular',
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 10,
  },
  highlightText: {
    color: '#aaad00',
    fontFamily: 'EstedadBold',
  },
});

export default StarPopover;
