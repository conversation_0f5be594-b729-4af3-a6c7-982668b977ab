import React, { useCallback, useEffect, useState } from "react";
import {
  View,
  Text,
  Pressable,
  Image,
  StyleProp,
  ViewStyle,
  Alert,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import styles from "../../styles/homeStyle";
import constants from "../../utils/constants";
import {
  MaterialIcons,
  FontAwesome,
  FontAwesome5,
  MaterialCommunityIcons,
  Foundation,
  Feather,
  Entypo,
  Ionicons,
} from "@expo/vector-icons";
import requestHelper from "../../utils/requestHelper";
import LastLessonCardStyles from "../../styles/LastLessonCardStyle";
import { convertToPersianNumber } from "../../utils/helpers";
import {
  StatBadgeProps,
  LastCompletedLessonData,
  Scenario,
} from "../../types/LastLesson";
import { useAppSelector, useAppDispatch } from "../../store/hooks";
import { updateCoins } from "../../store/slices/authSlice";
import InsufficientCoinsModal from "../modals/InsufficientCoinsModal";

const iconPackMap = {
  MaterialIcons,
  FontAwesome,
  FontAwesome5,
  MaterialCommunityIcons,
  Foundation,
  Feather,
  Entypo,
  Ionicons,
};

// Define interface for daily lesson data
interface DailyLessonData {
  scenario: Scenario;
  messageCount: number;
  GemCostPerLesson: number;
}

// Define the DynamicIcon component props
interface DynamicIconProps {
  pack: keyof typeof iconPackMap;
  name: string;
  size: number;
  color: string;
  style?: StyleProp<ViewStyle>;
}

interface DailyLessonCardProps {
  lastCompletedLesson: LastCompletedLessonData | null;
}

const DailyLessonCard = ({ lastCompletedLesson }: DailyLessonCardProps) => {
  const navigation = useNavigation<any>();
  const [localIsLoading, setLocalIsLoading] = useState(false);
  const [dailyLesson, setDailyLesson] = useState<DailyLessonData | null>(null);
  const { isPremium, coins } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const [showInsufficientCoins, setShowInsufficientCoins] = useState(false);

  const fetchDailyLesson = async () => {
    setLocalIsLoading(true);
    try {
      const response = await requestHelper("get", "/ai/dayli/lesson");
      setDailyLesson(response);
    } catch (error) {
      console.error("Error fetching daily lesson:", error);
    }
    setLocalIsLoading(false);
  };

  useFocusEffect(
    useCallback(() => {
      fetchDailyLesson();
    }, [])
  );
  const reduceGems = async () => {
    await requestHelper(
      "post",
      "/users/gems/reduce",
      { scenarioId: dailyLesson.scenario.id },
      null,
      null,
      false
    );
    dispatch(updateCoins(coins - dailyLesson.GemCostPerLesson));
  };

  const navigateToChat = () => {
    navigation.navigate("chat", {
      topicItem: dailyLesson.scenario,
      comesFrom: "DailyLesson",
    });
  };

  const checkAndStartLesson = async () => {
    if (!dailyLesson) return;
    if (isPremium) {
      // if (coins >= dailyLesson.GemCostPerLesson) {
      //   await reduceGems();
      // }
      navigateToChat();
      return;
    }

    if (!isPremium) {
      console.log("coins", coins);
      console.log("dailyLesson.GemCostPerLesson", dailyLesson.GemCostPerLesson);
      if (!dailyLesson.GemCostPerLesson) {
        dailyLesson.GemCostPerLesson = 25;
      }
      if (coins >= dailyLesson.GemCostPerLesson) {
        // await reduceGems();
        navigateToChat();
      } else {
        setShowInsufficientCoins(true);
      }
    }
  };

  const DynamicIcon = ({
    pack,
    name,
    size,
    color,
    style,
  }: DynamicIconProps) => {
    const IconComponent = iconPackMap[pack] || MaterialIcons;
    return (
      <IconComponent name={name} size={size} color={color} style={style} />
    );
  };

  // Check if there's an unfinished chat session based on backend data
  const hasUnfinishedChat = dailyLesson?.messageCount
    ? dailyLesson.messageCount > 0 &&
      dailyLesson.messageCount < constants.numberOfMessagesForAssessment
    : false;

  const progressWidth = dailyLesson?.messageCount
    ? Math.min(
        (dailyLesson.messageCount / constants.numberOfMessagesForAssessment) *
          100,
        100
      )
    : 0;

  const CardStatBadge = ({ icon, value, color, bgColor }: StatBadgeProps) => (
    <View
      style={[
        LastLessonCardStyles.cardStatBadge,
        { backgroundColor: bgColor, width: 70, height: 50, marginTop: 16 },
      ]}
    >
      {icon}
      <Text style={[LastLessonCardStyles.cardStatNumbers, { color: color }]}>
        {convertToPersianNumber(value.toString())}
      </Text>
    </View>
  );

  return (
    <>
      <LinearGradient
        colors={["#165cde", "#6498ed"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.nextLessonCard}
      >
        <Pressable onPress={checkAndStartLesson}>
          <View style={[styles.cardHeader]}>
            <Text style={[styles.cardHeaderLabel]}>
              {lastCompletedLesson === null ? "اولین تمرین" : "تمرین بعدی"}
            </Text>
          </View>

          {progressWidth > 0 && progressWidth < 100 && (
            <View style={styles.progressBar}>
              <View
                style={[
                  styles.progressDot,
                  {
                    width: `${progressWidth}%`,
                  },
                ]}
              />
            </View>
          )}

          <View
            style={{
              flexDirection: "row",
              alignItems: "flex-start",
              justifyContent: "flex-end",
              marginVertical: 12,
            }}
          >
            <Text style={styles.translatedTitle}>
              {localIsLoading
                ? "در حال بارگذاری..."
                : dailyLesson?.scenario?.translated_title ||
                  "در حال بارگذاری..."}
            </Text>

            {!localIsLoading && dailyLesson?.scenario?.icon && (
              <DynamicIcon
                pack={
                  (dailyLesson.scenario
                    .icon_pack as keyof typeof iconPackMap) || "MaterialIcons"
                }
                name={dailyLesson.scenario.icon}
                size={23}
                color="white"
                style={{
                  alignSelf: "center",
                  backgroundColor: "rgba(255, 255, 255, 0.2)",
                  borderRadius: 100,
                  padding: 6,
                }}
              />
            )}
          </View>
          <View
            style={{ flexDirection: "row", justifyContent: "space-between" }}
          >
            <View style={styles.talkButton}>
              <Text style={styles.talkButtonText}>
                {hasUnfinishedChat ? "ادامه تمرین" : "شروع درس"}
              </Text>
            </View>
          </View>
        </Pressable>
      </LinearGradient>
      <InsufficientCoinsModal
        visible={showInsufficientCoins}
        requiredCoins={dailyLesson?.GemCostPerLesson || 0}
        onClose={() => setShowInsufficientCoins(false)}
        onSubscribe={() => {
          setShowInsufficientCoins(false);
          navigation.navigate("profileTab", { screen: "subscription" });
        }}
      />
    </>
  );
};

export default DailyLessonCard;
