import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, Dimensions, Image } from 'react-native';
import Popover from 'react-native-popover-view';

interface GemPopoverProps {
  isVisible: boolean;
  onClose: () => void;
  gemCount: number;
  fromView: React.RefObject<View>;
}

const GemPopover: React.FC<GemPopoverProps> = ({
  isVisible,
  onClose,
  gemCount,
  fromView,
}) => {
  const [delayedVisible, setDelayedVisible] = useState(false);

  useEffect(() => {
    let timeout: NodeJS.Timeout;
    if (isVisible && fromView.current) {
      timeout = setTimeout(() => setDelayedVisible(true), 50);
    } else {
      setDelayedVisible(false);
    }
    return () => clearTimeout(timeout);
  }, [isVisible, fromView.current]);

  return (
    <>
      {fromView.current && (
        <Popover
          isVisible={delayedVisible}
          from={fromView}
          onRequestClose={onClose}
          popoverStyle={styles.popover}
        >
          <View style={styles.container}>
            <View style={styles.gemIconContainer}>
              <Image
                source={require('../../../assets/icons/gem.png')}
                style={{ width: 50, height: 50 }}
              />
            </View>
            <Text style={styles.titleText}>الماس‌ها</Text>
            
            <View style={styles.explanationContainer}>
              <Text style={styles.explanationText}>
                <Text style={styles.highlightText}>الماس‌ها</Text> برای شروع درس‌های جدید استفاده می‌شوند
              </Text>
              <Text style={styles.explanationText}>
                هر درس جدید <Text style={styles.highlightText}>۲۵ الماس</Text> از شما کم می‌کند
              </Text>
              <Text style={styles.explanationText}>
                با خرید اشتراک ویژه، الماس‌های شما <Text style={styles.highlightText}>نامحدود</Text> خواهد شد
              </Text>
            </View>
          </View>
        </Popover>
      )}
    </>
  );
};

const styles = StyleSheet.create({
  popover: {
    borderRadius: 16,
    padding: 0,
    width: Dimensions.get('window').width * 0.85,
    maxWidth: 400,
  },
  container: {
    padding: 20,
    alignItems: 'center',
  },
  gemIconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 5,
  },
  titleText: {
    fontFamily: 'EstedadBold',
    fontSize: 24,
    color: '#d303fc',
    marginBottom: 20,
  },
  explanationContainer: {
    borderTopWidth: 1,
    borderTopColor: '#eee',
    paddingTop: 15,
    width: '100%',
  },
  explanationText: {
    fontFamily: 'EstedadRegular',
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 10,
  },
  highlightText: {
    color: '#d303fc',
    fontFamily: 'EstedadBold',
  },
});

export default GemPopover;
