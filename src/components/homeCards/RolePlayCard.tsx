import React from "react";
import { View, Text, TouchableOpacity, Pressable } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import styles from "../../styles/homeStyle";

const RolePlayCard = () => {
  const navigation = useNavigation();

  return (
    <LinearGradient
      colors={["#c92271", "#e35d9c"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 0 }}
      style={styles.classicLessonCard}
    >
      <Pressable
        onPress={() => navigation.navigate("topic")}
      >
      <View style={styles.cardHeader}>
        <Text style={styles.cardHeaderLabel}>نقش آفرینی</Text>
        <Text style={styles.practiceSubtitle}>
          خودتو در موقعیت‌های مختلف قرار بده و سناریوها رو اجرا کن
        </Text>
      </View>

      <View
        style={styles.talkButton}
      >
        <Text style={styles.talkButtonText}>مشاهده همه</Text>
      </View>
      </Pressable>
    </LinearGradient>
  );
};

export default RolePlayCard;
