import React, { useEffect, useState, useRef } from "react";
import {
  Pressable,
  StyleSheet,
  Text,
  View,
  Image,
  TouchableOpacity,
} from "react-native";
import RequestHelper from "../../utils/requestHelper";
import {
  FontAwesome5,
  MaterialCommunityIcons,
  MaterialIcons,
} from "@expo/vector-icons";
import { convertToPersianNumber } from "../../utils/helpers";
import styles from "../../styles/GamAndStreakStyle";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { updateCoins } from "../../store/slices/authSlice";
import StreakPopover from "./StreakPopover";
import GemPopover from "./GemPopover";
import StarPopover from "./StarPopover";
import { useNavigation } from "@react-navigation/native";

// Define types for the components
type StatBadgeProps = {
  icon: React.ReactNode;
  value: number;
  color: string;
  bgColor: string;
  isStreak?: boolean;
  isGem?: boolean;
  isStar?: boolean;
};

const GamificationComponent = () => {
  const [stats, setStats] = useState({
    gamePoints: 0,
    gameLevel: 0,
    gameGems: 0,
    currentLevelPercent: 0,
    streak: 0,
    rank: 0,
    progress: 0,
    streakStatus: {
      saturday: false,
      sunday: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
    },
  });
  const [showStreakPopover, setShowStreakPopover] = useState(false);
  const [showGemPopover, setShowGemPopover] = useState(false);
  const [showStarPopover, setShowStarPopover] = useState(false);
  const streakBadgeRef = useRef(null);
  const gemBadgeRef = useRef(null);
  const starBadgeRef = useRef(null);
  const dispatch = useAppDispatch();
  const { coins, isPremium } = useAppSelector((state) => state.auth);
  const navigation = useNavigation<any>();

  const fetchGemificationStats = async () => {
    const stats = await RequestHelper("get", "/ai/gamification/stats");
    setStats(stats);
    dispatch(updateCoins(stats.gameGems));
  };

  useEffect(() => {
    fetchGemificationStats();
  }, []);

  // Stats badges with light backgrounds
  const StatBadge = ({
    icon,
    value,
    color,
    bgColor,
    isStreak,
    isGem,
    isStar,
  }: StatBadgeProps) => (
    <Pressable
      ref={
        isStreak
          ? streakBadgeRef
          : isGem
          ? gemBadgeRef
          : isStar
          ? starBadgeRef
          : null
      }
      onPress={
        isStreak
          ? () => setShowStreakPopover(true)
          : isGem
          ? () => setShowGemPopover(true)
          : isStar
          ? () => setShowStarPopover(true)
          : undefined
      }
      style={[styles.statBadge, { backgroundColor: bgColor }]}
    >
      {icon}
      <Text style={[styles.statNumbers, { color: color }]}>
        {convertToPersianNumber(value.toString())}
      </Text>
    </Pressable>
  );

  // Progress circle component

  return (
    <View style={styles.container}>
      {/* Top stats row */}
      <View style={styles.topStatsContainer}>
        {
          !isPremium && (
            <Pressable
              onPress={() => {
                navigation.navigate('profileTab', { screen: 'subscription' });


              }}
            >
              <Image
                source={require("../../../assets/icons/crown.png")}
                style={{ width: 32, height: 32, marginRight: 10 }}
              />
            </Pressable>
          )
          // <MaterialCommunityIcons name="crown-circle" size={38} color="#b0107d" />
        }
        <View style={styles.topStatsRow}>
          <StatBadge
            icon={<FontAwesome5 name="fire-alt" size={27} color="#ff3b30" />}
            value={stats.streak}
            color="#ff3b30"
            bgColor="rgba(255, 59, 48, 0.15)"
            isStreak={true}
          />
          <StatBadge
            icon={
              <Image
                source={require("../../../assets/icons/gem.png")}
                style={{ width: 18, height: 18 }}
              />
            }
            value={coins}
            color="#d303fc"
            bgColor="rgba(211, 3, 252, 0.15)"
            isGem={true}
          />
          <StatBadge
            icon={<MaterialIcons name="star" size={20} color="#FFD700" />}
            value={stats.gamePoints}
            color="#aaad00"
            bgColor="rgba(163, 163, 3, 0.15)"
            isStar={true}
          />
        </View>
      </View>

      {/* Streak Popover */}
      <StreakPopover
        isVisible={showStreakPopover}
        onClose={() => setShowStreakPopover(false)}
        streakCount={stats.streak}
        streakStatus={stats.streakStatus}
        fromView={streakBadgeRef}
      />

      {/* Gem Popover */}
      <GemPopover
        isVisible={showGemPopover}
        onClose={() => setShowGemPopover(false)}
        gemCount={stats.gameGems}
        fromView={gemBadgeRef}
      />

      {/* Star Popover */}
      <StarPopover
        isVisible={showStarPopover}
        onClose={() => setShowStarPopover(false)}
        pointsCount={stats.gamePoints}
        fromView={starBadgeRef}
      />
    </View>
  );
};

export default GamificationComponent;
