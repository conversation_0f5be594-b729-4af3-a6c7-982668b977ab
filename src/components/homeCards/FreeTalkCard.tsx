import React from "react";
import { View, Text, TouchableOpacity, Pressable } from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import styles from "../../styles/homeStyle";

const FreeTalkCard = () => {
  const navigation = useNavigation();

  return (
    <LinearGradient
      colors={["#8e34d9", "#b36ded"]}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.classicLessonCard}
    >
      <Pressable onPress={() => navigation.navigate("freeTalk")}>
        <View style={[styles.cardHeader, {minHeight: 100}]}>
          <Text style={styles.cardHeaderLabel}>گفتگوی آزاد</Text>
          <Text style={styles.practiceSubtitle}>
            راجع به هرچی دوست داری صحبت کن
          </Text>
        </View>

        <View style={styles.talkButton}>
          <Text style={styles.talkButtonText}>بیا حرف بزنیم</Text>
        </View>
      </Pressable>
    </LinearGradient>
  );
};

export default FreeTalkCard;
