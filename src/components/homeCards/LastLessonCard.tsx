import React, { useRef, useState } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Pressable,
  StyleProp,
  ViewStyle,
  Image,
} from "react-native";
import { LinearGradient } from "expo-linear-gradient";
import { useNavigation } from "@react-navigation/native";
import Popover, { PopoverPlacement } from "react-native-popover-view";

import styles from "../../styles/homeStyle";
import constants from "../../utils/constants";
import {
  MaterialIcons,
  FontAwesome,
  FontAwesome5,
  MaterialCommunityIcons,
  Foundation,
  Feather,
  Entypo,
  Ionicons,
} from "@expo/vector-icons";
import LastLessonCardStyles from "../../styles/LastLessonCardStyle";
import { convertToPersianNumber } from "../../utils/helpers";
import {
  LastCompletedLessonData,
  StatBadgeProps,
  ProgressCircleProps,
} from "../../types/LastLesson";

const iconPackMap = {
  MaterialIcons,
  FontAwesome,
  FontAwesome5,
  MaterialCommunityIcons,
  Foundation,
  Feather,
  Entypo,
  Ionicons,
};

interface LastLessonCardProps {
  lastCompletedLesson: LastCompletedLessonData;
}

const LastLessonCard = ({ lastCompletedLesson }: LastLessonCardProps) => {
  // Define the DynamicIcon component with proper types
  interface DynamicIconProps {
    pack: keyof typeof iconPackMap;
    name: string;
    size: number;
    color: string;
    style?: StyleProp<ViewStyle>;
  }

  const DynamicIcon = ({
    pack,
    name,
    size,
    color,
    style,
  }: DynamicIconProps) => {
    const IconComponent = iconPackMap[pack] || MaterialIcons;
    return (
      <IconComponent name={name} size={size} color={color} style={style} />
    );
  };

  // Stats badges with dark backgrounds for the card
  const CardStatBadge = ({ icon, value, color, bgColor }: StatBadgeProps) => (
    <View
      style={[LastLessonCardStyles.cardStatBadge, { backgroundColor: bgColor }]}
    >
      {icon}
      <Text style={[LastLessonCardStyles.cardStatNumbers, { color: color }]}>
        {convertToPersianNumber(value.toString())}
      </Text>
    </View>
  );

  const ProgressCircle = ({ percentage }: ProgressCircleProps) => (
    <View style={LastLessonCardStyles.progressCircleContainer}>
      <View style={styles.progressCircle}>
        <Text style={styles.progressText}>
          {/* {convertToPersianNumber(percentage.toString())}% */}
          {percentage.toString()}%
        </Text>
      </View>
    </View>
  );

  const navigation = useNavigation();

  // Popover state
  const [showPopover, setShowPopover] = useState<null | 'percent' | 'gems' | 'star'>(null);
  const percentIconRef = useRef(null);

  return (
    lastCompletedLesson && (
      <LinearGradient
        colors={["#0ba147", "#34ba6a"]}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={LastLessonCardStyles.dailyLessonCard}
      >
        <View style={styles.cardHeader}>
          <Text style={styles.cardHeaderLabel}>تمرین انجام شده</Text>
        </View>
        <View style={LastLessonCardStyles.cardContent}>
          <View style={LastLessonCardStyles.cardStatsRow}>
            {/* Percent Icon with Popover */}
            <Popover
              isVisible={showPopover === 'percent'}
              from={
                <TouchableOpacity
                  ref={percentIconRef}
                  onPress={() => setShowPopover('percent')}
                >
                  <CardStatBadge
                    icon={
                      <FontAwesome5 name="percent" size={14} color="#cfcfcf" />
                    }
                    value={
                      lastCompletedLesson?.assessment?.scores?.average || 0
                    }
                    color="#cfcfcf"
                    bgColor="rgba(43, 43, 43, 0.3)"
                  />
                </TouchableOpacity>
              }
              onRequestClose={() => setShowPopover(null)}
              placement={PopoverPlacement.BOTTOM}
            >
              <View style={{ padding: 10, maxWidth: 200 }}>
                <Text
                  style={{
                    color: "#333",
                    fontSize: 14,
                    fontFamily: "EstedadRegular",
                  }}
                >
                  این عدد میانگین نمره شما در این تمرین است.
                </Text>
              </View>
            </Popover>

            {/* Gems Icon with Popover */}
            <Popover
              isVisible={showPopover === 'gems'}
              from={
                <TouchableOpacity
                  onPress={() => setShowPopover('gems')}
                >
                  <CardStatBadge
                    icon={
                      <Image
                        source={require("../../../assets/icons/gem.png")}
                        style={{ width: 20, height: 20 }}
                      />
                    }
                    value={lastCompletedLesson?.assessment.rewards?.gemsEarned || 0}
                    color="#f4b9fa"
                    bgColor="rgba(43, 43, 43, 0.39)"
                  />
                </TouchableOpacity>
              }
              onRequestClose={() => setShowPopover(null)}
              placement={PopoverPlacement.BOTTOM}
            >
              <View style={{ padding: 10, maxWidth: 200 }}>
                <Text
                  style={{
                    color: "#333",
                    fontSize: 14,
                    fontFamily: "EstedadRegular",
                  }}
                >
                  الماس ها پاداشی برای عملکرد شما در این تمرین هستند.
                </Text>
              </View>
            </Popover>

            {/* Star Icon with Popover */}
            <Popover
              isVisible={showPopover === 'star'}
              from={
                <TouchableOpacity
                  onPress={() => setShowPopover('star')}
                >
                  <CardStatBadge
                    icon={<MaterialIcons name="star" size={18} color="#FFD700" />}
                    value={lastCompletedLesson?.assessment.rewards?.pointsEarned || 0}
                    color="#FFD700"
                    bgColor="rgba(43, 43, 43, 0.3)"
                  />
                </TouchableOpacity>
              }
              onRequestClose={() => setShowPopover(null)}
              placement={PopoverPlacement.BOTTOM}
            >
              <View style={{ padding: 10, maxWidth: 200 }}>
                <Text
                  style={{
                    color: "#333",
                    fontSize: 14,
                    fontFamily: "EstedadRegular",
                  }}
                >
                  ستاره ها امتیاز شما در این تمرین را نشان می دهند.
                </Text>
              </View>
            </Popover>
          </View>
          <View style={LastLessonCardStyles.buttonsRow}>
            <Pressable
              onPress={() => {
                navigation.navigate("chat", {
                  topicItem: lastCompletedLesson.scenario,
                  comesFrom: "LastLesson",
                });
              }}
            >
              <View style={styles.lessonButton}>
                <Text style={[styles.talkButtonText, { fontSize: 18 }]}>
                  مرور
                </Text>
              </View>
            </Pressable>
          </View>
        </View>
      </LinearGradient>
    )
  );
};

export default LastLessonCard;
