import React, { useEffect, useRef } from 'react';
import { View, StyleSheet, Animated } from 'react-native';

const ProgressBar = ({ progress }) => {
  const widthAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    Animated.timing(widthAnim, {
      toValue: progress * 100,
      duration: 500,
      useNativeDriver: false,
    }).start();
  }, [progress]);

  return (
    <View style={styles.container}>
      <Animated.View style={[styles.filler, { width: widthAnim.interpolate({
        inputRange: [0, 100],
        outputRange: ['0%', '100%']
      }) }]} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 10,
    width: '100%',
    backgroundColor: '#e0e0df',
    borderRadius: 5,
    overflow: 'hidden',
    marginBottom: 10,
  },
  filler: {
    height: '100%',
    backgroundColor: 'rgb(14 190 117)',
    borderRadius: 5,
  },
});

export default ProgressBar;