import React from "react";
import { View, Text, StyleSheet } from "react-native";

interface ScoreResultProps {
  scores: {
    average: number;
    [key: string]: any;
  };
}

const ScoreResult = ({ scores }: ScoreResultProps) => {
  return (
    <View style={styles.card}>
      <Text style={styles.scoreText}>نمره: {scores.average}%</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  card: {
    padding: 13,
    margin: 10,
    backgroundColor: "green",
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.8,
    shadowRadius: 2,
    elevation: 5,
  },
  scoreText: {
    fontSize: 20,
    fontFamily: "EstedadRegular",
    textAlign: "center",
    color: "white",
  },
});

export default ScoreResult;
