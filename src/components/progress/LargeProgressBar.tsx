import React, { memo } from "react";
import { useEffect, useRef } from "react";
import { Animated, Text, View } from "react-native";
import ProgressBar from "react-native-progress/Bar";

interface LargeProgressBarProps {
  progress: number;
  refreshing: boolean;
}

const LargeProgressBar = memo(({ progress, refreshing }: LargeProgressBarProps) => {

  const animatedValue = useRef(new Animated.Value(0)).current;
  useEffect(() => {
    Animated.timing(animatedValue, {
      toValue: progress,
      duration: 2000,
      useNativeDriver: false,
    }).start();
  }, [progress, refreshing]);

  const progressPercent = animatedValue.interpolate({
    inputRange: [0, 1],
    outputRange: ["0%", "100%"],
  });
  const filledColor = "rgb(14 190 117)";
  return (
    <View>
      <View
        style={{
          justifyContent: "center",
          marginBottom: 30,
          marginTop: 10,
          marginHorizontal: "auto",
          // paddingHorizontal: 25,
          width: "95%",
        }}
      >
        <ProgressBar
          borderRadius={10}
          progress={progress}
          height={13}
          width={null}
          color={filledColor}
          unfilledColor="white"
          borderWidth={0.5}
          borderColor={"#4169E1"}
          animationConfig={{ duration: 2000 }}
          animationType="timing"
          animated={true}
        />
        <Animated.View
          style={{
            backgroundColor: filledColor,
            position: "absolute",
            left: progressPercent,
            width: 45,
            height: 21,
            borderRadius: 25,
            justifyContent: "center",
            alignItems: "center",
            transform: [{ translateX: -3 }],
          }}
        >
          <Text
            style={{
              color: "white",
              fontSize: 12,
              textAlign: "center",
              lineHeight: 22,
            }}
          >
            {(progress * 100).toFixed(0)}%
          </Text>
        </Animated.View>
      </View>
    </View>
  );
});

export default LargeProgressBar;
