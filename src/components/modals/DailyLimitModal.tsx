import React from "react";
import { Modal, View, Text, TouchableOpacity, StyleSheet, Image } from "react-native";
import { useAppSelector } from "../../store/hooks";

interface DailyLimitModalProps {
  visible: boolean;
  onClose: () => void;
  onSubscribe: () => void;
}

const DailyLimitModal = ({
  visible,
  onClose,
  onSubscribe,
}: DailyLimitModalProps) => {
  const { isPremium } = useAppSelector((state) => state.auth);

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.container}>
          <Image source={require("../../../assets/icons/crown.png")} style={styles.icon} />
          <Text style={styles.title}>محدودیت روزانه</Text>
          <Text style={styles.message}>
            {isPremium 
              ? "شما به حد مجاز روزانه خود رسیده‌اید. لطفاً فردا دوباره تلاش کنید."
              : "شما به حد مجاز رایگان روزانه خود رسیده‌اید. با خرید اشتراک می‌توانید بدون محدودیت ادامه دهید."
            }
          </Text>
          {!isPremium && (
            <View style={styles.buttonRow}>
              <TouchableOpacity style={styles.cancelButton} onPress={onClose}>
                <Text style={styles.cancelText}>بعدا</Text>
              </TouchableOpacity>
              <TouchableOpacity style={styles.subscribeButton} onPress={onSubscribe}>
                <Text style={styles.subscribeText}>خرید اشتراک</Text>
              </TouchableOpacity>
            </View>
          )}
          {isPremium && (
            <TouchableOpacity style={styles.okButton} onPress={onClose}>
              <Text style={styles.okText}>متوجه شدم</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0,0,0,0.4)",
    justifyContent: "center",
    alignItems: "center",
  },
  container: {
    backgroundColor: "#fff",
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    width: 320,
    elevation: 8,
  },
  icon: {
    width: 40,
    height: 40,
    marginBottom: 12,
  },
  title: {
    fontSize: 20,
    color: "#ff9800",
    marginBottom: 8,
    fontFamily: "EstedadRegular",
  },
  message: {
    fontSize: 16,
    color: "#333",
    textAlign: "center",
    marginBottom: 20,
    fontFamily: "EstedadRegular",
    lineHeight: 24,
  },
  buttonRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "100%",
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#eee",
    borderRadius: 8,
    paddingVertical: 10,
    marginRight: 8,
    alignItems: "center",
  },
  subscribeButton: {
    flex: 1,
    backgroundColor: "#4A84F0",
    borderRadius: 8,
    paddingVertical: 10,
    marginLeft: 8,
    alignItems: "center",
  },
  okButton: {
    backgroundColor: "#4A84F0",
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 30,
    alignItems: "center",
    width: "100%",
  },
  cancelText: {
    color: "#333",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
  subscribeText: {
    color: "#fff",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
  okText: {
    color: "#fff",
    fontSize: 16,
    fontFamily: "EstedadRegular",
  },
});

export default DailyLimitModal;