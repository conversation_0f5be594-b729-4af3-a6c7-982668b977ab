import React from "react";
import {
  View,
  Text,
  StyleSheet,
  Modal,
  TouchableOpacity,
  Pressable,
} from "react-native";
import { MaterialCommunityIcons } from "@expo/vector-icons";

interface ScenarioLockModalProps {
  isVisible: boolean;
  onClose: () => void;
  onSubscribe: () => void;
}

const ScenarioLockModal: React.FC<ScenarioLockModalProps> = ({
  isVisible,
  onClose,
  onSubscribe,
}) => {
  return (
    <Modal
      transparent={true}
      visible={isVisible}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalBackground}>
        <View style={styles.alertContainer}>
          <View style={styles.modalHeader}>
            <Pressable
              hitSlop={{ top: 30, bottom: 30, left: 30, right: 30 }}
              onPress={onClose}
            >
              <MaterialCommunityIcons
                name="close"
                size={22}
                color="white"
                style={{
                  marginLeft: 5,
                }}
              />
            </Pressable>
            <Text
              style={{
                color: "white",
                fontFamily: "EstedadRegular",
                fontSize: 18,
                marginRight: 10,
                textAlign: "center",
              }}
            >
              دسترسی محدود
            </Text>
          </View>
          <View style={styles.content}>
            <Text style={styles.messageText}>
              برای دسترسی به این سناریو، ابتدا باید سناریوی قبلی را تکمیل کنید یا اشتراک ویژه تهیه نمایید.
            </Text>
            <TouchableOpacity
              onPress={onSubscribe}
              style={styles.subscribeButton}
            >
              <Text style={styles.buttonText}>تهیه اشتراک ویژه</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
            >
              <Text style={styles.closeButtonText}>بازگشت</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  alertContainer: {
    width: "85%",
    backgroundColor: "white",
    borderRadius: 15,
    overflow: "hidden",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "#4e58de",
    paddingVertical: 12,
    paddingHorizontal: 15,
  },
  content: {
    padding: 20,
    alignItems: "center",
  },
  messageText: {
    fontFamily: "EstedadRegular",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 20,
    lineHeight: 24,
  },
  subscribeButton: {
    backgroundColor: "#4e58de",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    width: "100%",
    alignItems: "center",
    marginBottom: 10,
  },
  buttonText: {
    color: "white",
    fontFamily: "EstedadRegular",
    fontSize: 16,
  },
  closeButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    width: "100%",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#ccc",
  },
  closeButtonText: {
    color: "#333",
    fontFamily: "EstedadRegular",
    fontSize: 16,
  },
});

export default ScenarioLockModal;
