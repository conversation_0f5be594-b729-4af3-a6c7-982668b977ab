import { v4 as uuidv4 } from "uuid";
import llmResponeTrimer from "../../utils/llmResponeTrimer";
import Toast from "react-native-toast-message";
import * as Speech from "expo-speech";
import {
  simpleUUID,
  llmRequest,
  getHistoryFromTranscription,
  getChats,
  reduceGemsForNewLesson,
} from "./chatUtils";
import constants from "../../utils/constants";


// Define interfaces for better type safety
interface TranscriptionItem {
  key: number;
  type: "req" | "res" | "loading";
  text: string;
  isTopic: boolean;
  isRelevant?: boolean; // Optional properties based on usage
  correctedText?: string;
  _id?: string;
}

interface ChatMessage {
  _id?: string;
  chatId: string;
  role: "user" | "assistant";
  text: string;
  isRelevant?: boolean;
  correctedText?: string;
}

interface TopicItem {
  _id: string;
  title: string;
  scenario: string;
  // Add other properties if needed
}

/**
 * Adds a request to the chat history
 */
export const addReqToHistory = (
  text: string,
  isTopic: boolean,
  transcriptionResult: any[],
  setTranscriptionResult: (result: any[]) => void,
  setRequested: (requested: boolean) => void
) => {
  setRequested(false);
  let newArr1 = isTopic ? [] : [...transcriptionResult];
  const reqKey = Date.now() + Math.floor(Math.random() * 1000);
  const loadingKey = reqKey + 1;
  let newArr2 = [
    ...newArr1,
    {
      key: reqKey,
      type: "req",
      text,
      isTopic,
    },
    {
      key: loadingKey,
      type: "loading",
      text: "",
      isTopic: false,
    },
  ];
  setTranscriptionResult(newArr2);
};

/**
 * Adds a chat message to the history
 */
export const addChatHistory = (
  text: string,
  type: string,
  transcriptionResult: any[],
  setTranscriptionResult: (result: any[]) => void,
  isTopic: boolean = false,
  key: number = Date.now() + Math.floor(Math.random() * 1000),
  removeLoading: boolean = false
) => {
  let newArr3 = [...transcriptionResult];
  if (removeLoading) {
    newArr3 = newArr3.filter((item) => item.type !== "loading");
  }
  newArr3 = [
    ...newArr3,
    {
      key,
      type,
      text,
      isTopic,
    },
  ];
  setTranscriptionResult(newArr3);
};

/**
 * Starts a new chat
 */
export const startNewChat = async (
  topicItem: any,
  setChatId: (id: string) => void,
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  setRequested: (requested: boolean) => void,
  dispatch: any, // Consider defining a type for dispatch if possible
  initNewChat: any // Consider defining a type for initNewChat action creator
) => {
  let uuid;
  try {
    uuid = uuidv4();
  } catch (err) {
    uuid = simpleUUID();
    console.log("err4", err);
  }
  setChatId(uuid);
  addReqToHistory(
    topicItem.scenario,
    true,
    [],
    setTranscriptionResult,
    setRequested
  );
  dispatch(initNewChat({ newChat: false }));
};

/**
 * Starts a topic
 */
export const startTopic = async (
  newChat: boolean,
  topicItem: any,
  setChatId: (id: string) => void,
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  setRequested: (requested: boolean) => void,
  setScores: (scores: any) => void, // Define type for scores if possible
  dispatch: any, // Consider defining a type for dispatch
  initNewChat: any, // Consider defining a type for initNewChat action creator
  keySize: number,
  level: string,
  approvedNewChat: boolean,
  isPremium: boolean = false,
  currentGems: number = 0
) => {
  if (approvedNewChat) {
    startNewChat(
      topicItem,
      setChatId,
      setTranscriptionResult,
      setRequested,
      dispatch,
      initNewChat
    );
    return;
  }

  const { lastMessages, scores } = await getChats(topicItem._id);
  setScores(scores);

  if (lastMessages && lastMessages.length > 0) {
    lastMessages.shift(); // Removes the first message (topic scenario?)
    const chatsArray: TranscriptionItem[] = lastMessages.map(
      (chat: ChatMessage) => {
        let sentence = chat.text;
        if ("assistant" === chat.role) {
          sentence = llmResponeTrimer(chat.text, level);
        }
        if ("user" === chat.role) {
          sentence = sentence.replace(" (answer very short and brief)", "");
        }

        return {
          key: chat._id || Math.floor(Math.random() * keySize),
          type: "user" === chat.role ? "req" : "res",
          text: sentence,
          isTopic: false,
          isRelevant: chat.isRelevant,
          correctedText: chat.correctedText,
          _id: chat._id,
        };
      }
    );
    setChatId(lastMessages[0].chatId);
    setTranscriptionResult(chatsArray);
  } else {
    // No existing messages - this is a new lesson/topic
    // Reduce gems before starting the chat
    const gemReductionResult = await reduceGemsForNewLesson(
      topicItem._id,
      isPremium,
      currentGems,
      dispatch,
    );

    if (!gemReductionResult.success) {
      console.error("Failed to reduce gems:", gemReductionResult.error);
      // You might want to show an error message to the user here
      // For now, we'll still allow the chat to start
    }

    startNewChat(
      topicItem,
      setChatId,
      setTranscriptionResult,
      setRequested,
      dispatch,
      initNewChat
    );
  }
};

/**
 * Handles chat submission
 */
export const submitChat = (
  inputValue: string,
  setTextSubmitted: (submitted: boolean) => void,
  setInputValue: (value: string) => void,
  setIsSpeaking: (speaking: any) => void,
  addReqToHistoryFn: (text: string, isTopic: boolean) => void
) => {
  if (inputValue.length > 0) {
    const invalidChars = inputValue.match(/[^a-zA-Z0-9.,!?'"-_@$%#&()\s]/g);
    if (invalidChars?.length) {
      Toast.show({
        type: "error",
        text1: "فقط از حروف انگلیسی استفاده کنید",
      });
      return;
    }
    setTextSubmitted(true);
    Speech.stop();
    setIsSpeaking(null);
    setInputValue("");
    addReqToHistoryFn(inputValue, false);
  }
};

/**
 * Handles chat request
 */
export const chatRequest = async (
  loadingKey: number,
  transcriptionResult: any[],
  chatId: string,
  topicItem: TopicItem,
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  setPlaySpeech: (play: boolean) => void,
  setHasReachedDailyLimit: (reached: boolean) => void,
  setShowCongratulations: (show: boolean) => void,
  level: string,
  navigation?: any,
  setChatFinished?: (finished: boolean) => void
) => {
  try {
    const prompt = getHistoryFromTranscription(transcriptionResult);
    let { message, messageId } = await llmRequest(
      prompt,
      chatId,
      topicItem._id,
      false,
      () => setHasReachedDailyLimit(true)
    );

    if (!message || message.content === "") {
      ({ message, messageId } = await llmRequest(
        prompt,
        chatId,
        topicItem._id,
        true
      ));
    }

    let trimmedSentence = llmResponeTrimer(message.content, level);

    addChatHistory(
      trimmedSentence,
      "res",
      transcriptionResult,
      setTranscriptionResult,
      false,
      loadingKey,
      true
    );

    setPlaySpeech(true);
    checkIfReachedLastMessage(
      topicItem,
      transcriptionResult,
      setShowCongratulations,
      navigation,
      chatId,
      setChatFinished
    );
  } catch (err) {
    console.log("err3", err);
  }
};

export const checkIfReachedLastMessage = (
  topicItem: TopicItem,
  transcriptionResult: TranscriptionItem[],
  setShowCongratulations: (show: boolean) => void,
  navigation?: any,
  chatId?: string,
  setChatFinished?: (finished: boolean) => void
) => {
  // for assessment
  if (topicItem.title !== "free talk") {
    const userMessages = transcriptionResult.filter(
      (item: TranscriptionItem) => item.type === "req" && !item.isTopic
    );
    if (userMessages.length >= constants.numberOfMessagesForAssessment) {
      // Skip congratulations and directly navigate to assessment screen
      if (navigation && chatId && setChatFinished) {
        // Mark chat as finished
        setChatFinished(true);
        Speech.stop();
        // Navigate to assessment screen
        navigation.navigate("gamification", {
          chatId,
          topicItem,
          transcriptionResult
        });
      } else {
        // Fallback to showing congratulations if navigation is not available
        setShowCongratulations(true);
      }
    }
  }
};

/**
 * Requests another message from the AI
 */
export const requestAgain = async (
  transcriptionResult: any[],
  chatId: string,
  topicItem: TopicItem,
  setRequested: (requested: boolean) => void,
  setTranscriptionResult: (result: TranscriptionItem[]) => void,
  setPlaySpeech: (play: boolean) => void,
  setHasReachedDailyLimit: (reached: boolean) => void
) => {
  try {
    setRequested(true);
    const prompt = getHistoryFromTranscription(transcriptionResult);
    let newArr1 = [...transcriptionResult];
    const loadingKey = Date.now() + Math.floor(Math.random() * 1000);
    const newArr2 = [
      ...newArr1,
      {
        key: loadingKey,
        type: "loading",
        text: "",
        isTopic: false,
      },
    ];
    setTranscriptionResult(newArr2);
    const promptCopy = [...prompt];
    promptCopy.push({
      role: "user",
      content: "(Ask a question from the user)",
    });

    let { message } = await llmRequest(
      promptCopy,
      chatId,
      topicItem._id,
      true,
      () => setHasReachedDailyLimit(true)
    );

    if (!message || message.content === "") {
      console.log("empty message in request again");
      console.log("message", message);
      ({ message } = await llmRequest(promptCopy, chatId, topicItem._id, true));
    }

    const trimed = message.content.trim();
    const removeSymbols = trimed.replace(/[^a-zA-Z0-9 _.?!,:'$€%]/g, "");

    addChatHistory(
      removeSymbols,
      "res",
      transcriptionResult,
      setTranscriptionResult,
      false,
      loadingKey,
      true
    );

    setPlaySpeech(true);
  } catch (err) {
    console.log("err2", err);
  }
};

