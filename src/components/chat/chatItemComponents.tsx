import React from "react";
import { View, Pressable, ActivityIndicator, Image, Text } from "react-native";
import {
  MaterialCommunityIcons,
  AntDesign,
  Octicons,
  FontAwesome5,
} from "@expo/vector-icons";
import { Animated } from "react-native";
import { FontAwesome } from "@expo/vector-icons";

export const BotComponent = ({
  item,
  isLastMessage,
  nextItem,
  previousItem,
  styles,
}) => {
  if (item.type === "res" || item.type === "loading" || item.type === "loadingRes") {
    if (
      isLastMessage ||
      (nextItem && nextItem.type !== "res") ||
      (previousItem && (previousItem.type === "res" || previousItem.isTopic)) ||
      (!previousItem && !nextItem)
    ) {
      return (
        <Image
          style={[styles.profilePicture, { marginRight: 7, marginBottom: 3 }]}
          source={require("../../../assets/icons/bot.jpg")}
        />
      );
    } else {
      return (
        <View
          style={[styles.profilePicture, { marginRight: 7, marginBottom: 3 }]}
        ></View>
      );
    }
  }
  return null;
};

export const GrammerComponent = ({
  item,
  isGrammarCorrect,
  reward,
  setGrammarModalVisible,
}) => {
  const rewardAnimation = React.useRef(new Animated.Value(0)).current;

  React.useEffect(() => {
    if (isGrammarCorrect && reward > 0) {
      Animated.spring(rewardAnimation, {
        toValue: 1,
        useNativeDriver: true,
        tension: 50,
        friction: 7,
      }).start();
    }
  }, [isGrammarCorrect, reward]);

  if (item.type === "req") {
    return (
      <View
        style={{
          marginRight: 10,
          justifyContent: "center",
          marginBottom: 8,
        }}
      >
        {isGrammarCorrect === null ? (
          <ActivityIndicator size="small" color="#6200EE" />
        ) : (
          <Pressable
            onPress={() => !isGrammarCorrect && setGrammarModalVisible(true)}
            style={({ pressed }) => [
              {
                backgroundColor: isGrammarCorrect ? "#e8f5e9" : "#ffebee",
                borderRadius: 12,
                padding: 8,
                elevation: pressed ? 0 : 2,
                shadowColor: "#000",
                shadowOffset: { width: 0, height: 1 },
                shadowOpacity: 0.2,
                shadowRadius: 1.41,
                transform: [{ scale: pressed ? 0.96 : 1 }],
              },
            ]}
          >
            <AntDesign
              name={isGrammarCorrect ? "checkcircle" : "closecircle"}
              size={22}
              color={isGrammarCorrect ? "#4caf50" : "#f44336"}
              style={{ opacity: 0.9 }}
            />
          </Pressable>
        )}
      </View>
    );
  }
  return null;
};

export const AvatarComponent = ({ item, photo, styles }) => {
  if (item.type === "req" || item.type === "loadingReq") {
    if (photo) {
      return (
        <Image
          style={[styles.profilePicture, { marginLeft: 7 }]}
          source={{ uri: photo }}
        />
      );
    } else {
      return (
        <FontAwesome
          name="user-circle-o"
          size={35}
          color="blue"
          style={{ marginRight: 7, marginBottom: 5 }}
        />
      );
    }
  }
  return null;
};
