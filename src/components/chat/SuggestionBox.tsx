import React, { useEffect, useRef, useState } from "react";
import {
  Text,
  View,
  StyleSheet,
  Pressable,
  Animated,
  ActivityIndicator,
  FlatList,
} from "react-native";
import { MaterialIcons } from "@expo/vector-icons";
import RequestHelper from "../../utils/requestHelper";

type MyComponentProps = {
  showSuggestion: boolean;
  isLastMessage: boolean;
  item: object;
  chatId: string;
  flatListRef: React.RefObject<FlatList>;
  lastIndex: number;
  ScrollEnd: () => void;
};

const MyComponent = ({
  showSuggestion,
  isLastMessage,
  item,
  chatId,
  flatListRef,
  lastIndex,
  ScrollEnd,
}: MyComponentProps) => {
  const [suggestions, setSuggestions] = useState(null);
  const [loadingSuggestions, setLoadingSuggestions] = useState(false);
  const [translatedText, setTranslatedText] = useState("");
  const [loadingTranslations, setLoadingTranslations] = useState(false);
  const [showOriginalText, setShowOriginalText] = useState(true);
  const [currentSuggestionIndex, setCurrentSuggestionIndex] = useState(0);
  const slideAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (showSuggestion) {
      setTimeout(() => {
      }, 100);
      Animated.timing(slideAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
      getSuggestions();
      setShowOriginalText(true);
      setTranslatedText("");
    }
  }, [showSuggestion]);

  useEffect(() => {
    // Reset translation state when changing suggestions
    setTranslatedText("");
    setShowOriginalText(true);
  }, [currentSuggestionIndex]);
  useEffect(() => {
  }, [showOriginalText]);
  async function getSuggestions() {
    setSuggestions(null);
    setLoadingSuggestions(true);
    const message = await RequestHelper(
      "get",
      `/ai/prompt/suggestion/?chatId=${chatId}`
    );
    setLoadingSuggestions(false);
    setSuggestions(message);
  }

  async function getTranslation(text: string) {
    setLoadingTranslations(true);
    const {
      message: { content },
    } = await RequestHelper("get", `/ai/prompt/translate/?q=${text}`);
    setLoadingTranslations(false);
    setShowOriginalText(false);
    setTranslatedText(content.trim());
  }

  const getSuggestionsWithTranslation = () => {
    if (suggestions && suggestions.length === 0) {
      return "پیشنهادی یافت نشد";
    }
    if (loadingSuggestions) {
      return <ActivityIndicator size="small" color="#6200EE" />;
    }
    if (suggestions && suggestions.length > 0) {
      return suggestions[currentSuggestionIndex];
    }
    // else {
    //   if (translatedText) {
    //     return translatedText;
    //   }
    // }
  };

  return (
    showSuggestion &&
    isLastMessage &&
    item.type === "res" && (
      <Animated.View
        style={[
          styles.suggestionBox,
          {
            transform: [
              {
                translateY: slideAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: [-100, 0], // Box will slide from -100px to its position
                }),
              },
            ],
          },
        ]}
      >
        <Text
          style={{
            fontSize: 16,
            paddingHorizontal: 10,
            width: "100%",
            color: "#10316b",
            paddingBottom: 13,
            fontFamily: "EstedadRegular", 
          }}
        >
          سعی کن بگی:
        </Text>

        <View style={{ flexDirection: 'row', alignItems: 'center', paddingHorizontal: 10 }}>
          <MaterialIcons
            onPress={() => {
              if (suggestions && suggestions.length > 0) {
                setCurrentSuggestionIndex((prev) => (prev - 1 + suggestions.length) % suggestions.length);
              }
            }}
            name="arrow-back-ios"
            size={21}
            color="#0675bf"
          />
          <Text
            style={{
              flex: 1,
              fontSize: 16,
              textAlign: 'center',
              fontFamily: showOriginalText ? undefined : "EstedadRegular",
            }}
          >
            {getSuggestionsWithTranslation()}
          </Text>
          <MaterialIcons
            onPress={() => {
              if (suggestions && suggestions.length > 0) {
                setCurrentSuggestionIndex((prev) => (prev + 1) % suggestions.length);
              }
            }}
            name="arrow-forward-ios"
            size={21}
            color="#0675bf"
          />
        </View>

        <View style={{ alignItems: 'center', marginTop: 10 }}>
          {loadingTranslations ? (
            <ActivityIndicator size="small" color="#6200EE" />
          ) : (
            <MaterialIcons
              onPress={() => {
                getTranslation(suggestions[currentSuggestionIndex]);
              }}
              name="translate"
              size={21}
              color="#0675bf"
            />
          )}
        </View>

        {translatedText && !showOriginalText && (
          <>
            <View
              style={{
                borderBottomWidth: 1,
                borderColor: "#ccc",
                marginVertical: 5,
                paddingHorizontal: 10,
                width: "90%",
                alignItems: "center",
                marginHorizontal: "auto",
              }}
            />
            <Text
              style={{
                fontSize: 14,
                fontFamily: "EstedadRegular",
              }}
            >
              {translatedText}
            </Text>
          </>
        )}
        {/* <Icons /> */}
      </Animated.View>
    )
  );
};

const styles = StyleSheet.create({
  suggestionBox: {
    marginTop: 10,
    borderRadius: 15,
    minWidth: "60%",
    maxWidth: "80%",
    minHeight: 100,
    borderWidth: 0.7,
    borderStyle: "dashed",
    padding: 10,
  },
  divider: {
    height: 1,
    backgroundColor: "#CCC",
    marginVertical: 10,
  },
});

export default MyComponent;
