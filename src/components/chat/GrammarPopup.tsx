import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  ActivityIndicator,
  Pressable,
  FlatList,
} from "react-native";
import { MaterialCommunityIcons, Ionicons } from "@expo/vector-icons";
import RequestHelper from "../../utils/requestHelper";
import GrammarCache from "../../utils/grammarCache";

interface GrammarPopupProps {
  visible: boolean;
  originalText: string;
  onClose: () => void;
  correctedText: string;
}

const GrammarPopup: React.FC<GrammarPopupProps> = ({
  visible,
  originalText,
  onClose,
  correctedText,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [explanation, setExplanation] = useState("");
  const [activeTab, setActiveTab] = useState<"grammar" | "refine">("grammar");
  const [refinements, setRefinements] = useState<string[]>([]);
  const [loadingRefinements, setLoadingRefinements] = useState(false);
  const [grammarFetched, setGrammarFetched] = useState(false);
  const [refinementsFetched, setRefinementsFetched] = useState(false);

  useEffect(() => {
    if (visible && originalText) {
      if (activeTab === "grammar" && !grammarFetched) {
        fetchGrammarExplanation();
      } else if (activeTab === "refine" && !refinementsFetched) {
        fetchRefinements();
      }
    }
  }, [visible, originalText, activeTab]);

  const formatText = (text) => {
    return text
      .split("\n") // Split the text into lines
      .map((line) => "\u200F" + line) // Add RLM to each line
      .join("\n"); // Rejoin the text
  };

  const fetchGrammarExplanation = async () => {
    setLoading(true);
    setError(null);
    setExplanation("");

    // Check cache first
    const cachedExplanation = GrammarCache.getGrammarExplanation(originalText);
    if (cachedExplanation) {
      setExplanation(formatText(cachedExplanation));
      setGrammarFetched(true);
      setLoading(false);
      return;
    }

    try {
      const { result: fetchedExplanation } = await RequestHelper(
        "post",
        `/ai/grammar/explain`,
        {
          text: originalText,
        }
      );
      setExplanation(formatText(fetchedExplanation));
      setGrammarFetched(true);
      // Cache the result
      GrammarCache.setGrammarExplanation(
        originalText,
        formatText(fetchedExplanation)
      );
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred.");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const fetchRefinements = async () => {
    setLoadingRefinements(true);
    setError(null);

    // Check cache first
    const cachedRefinements = GrammarCache.getRefinements(originalText);
    if (cachedRefinements) {
      setRefinements(cachedRefinements);
      setRefinementsFetched(true);
      setLoadingRefinements(false);
      return;
    }

    try {
      const { alternatives: fetchedRefinements } = await RequestHelper(
        "post",
        `/rephrase/rephraseText`,
        {
          text: originalText,
        }
      );
      const refinementsArray = Array.isArray(fetchedRefinements)
        ? fetchedRefinements
        : [];
      setRefinements(refinementsArray);
      setRefinementsFetched(true);
      // Cache the result
      GrammarCache.setRefinements(originalText, refinementsArray);
    } catch (err: any) {
      setError(err.message || "An unexpected error occurred.");
      console.error(err);
    } finally {
      setLoadingRefinements(false);
    }
  };

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={visible}
      onRequestClose={onClose}
    >
      <View style={styles.centeredView}>
        <View style={styles.modalView}>
          <View style={styles.tabContainer}>
            <Pressable
              style={[styles.tab, activeTab === "refine" && styles.activeTab]}
              onPress={() => setActiveTab("refine")}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === "refine" && styles.activeTabText,
                ]}
              >
                جایگزین
              </Text>
            </Pressable>
            <Pressable
              style={[styles.tab, activeTab === "grammar" && styles.activeTab]}
              onPress={() => setActiveTab("grammar")}
            >
              <Text
                style={[
                  styles.tabText,
                  activeTab === "grammar" && styles.activeTabText,
                ]}
              >
                گرامر
              </Text>
            </Pressable>
          </View>

          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
          >
            {activeTab === "grammar" ? (
              <View>
                <View style={styles.errorContainer}>
                  <View style={styles.errorIconContainer}>
                    <MaterialCommunityIcons
                      name="close"
                      size={20}
                      color="#FF0000"
                    />
                  </View>
                  <Text style={styles.errorText}>{originalText}</Text>
                </View>

                <View style={styles.correctionContainer}>
                  <View style={styles.correctionIconContainer}>
                    <MaterialCommunityIcons
                      name="check"
                      size={20}
                      color="#4CAF50"
                    />
                  </View>
                  <Text style={styles.correctionText}>{correctedText}</Text>
                </View>

                <View style={styles.reasonContainer}>
                  <Text style={styles.reasonLabel}>دلیل خطا</Text>
                  {loading ? (
                    <ActivityIndicator size="small" color="#0000ff" />
                  ) : (
                    <View style={{ flexDirection: "row-reverse" }}>
                      <Text style={styles.reasonText}>
                        {explanation}
                      </Text>
                    </View>
                  )}
                </View>
              </View>
            ) : (
              <View style={styles.refineContainer}>
                {loadingRefinements ? (
                  <ActivityIndicator size="large" color="#007AFF" />
                ) : error ? (
                  <Text style={styles.errorMessage}>{error}</Text>
                ) : (
                  <View style={{}}>
                    <Text
                      style={{
                        fontSize: 18,
                        color: "#0272bd",
                        fontFamily: "EstedadBold",
                        textAlign: "center",
                      }}
                    >
                      جملات جایگزین بجای
                    </Text>
                    <Text
                      style={{
                        fontSize: 16,
                        color: "#0272bd",
                        textAlign: "center",
                        marginBottom: 20,
                      }}
                    >
                      {originalText}
                    </Text>
                    <FlatList
                      data={refinements}
                      keyExtractor={(item, index) => index.toString()}
                      renderItem={({ item, index }) => (
                        <View>
                          <View style={styles.refinementItem}>
                            <Text style={styles.refinementText}>{item}</Text>
                          </View>
                          {index < refinements.length - 1 && (
                            <View style={styles.separator} />
                          )}
                        </View>
                      )}
                      showsVerticalScrollIndicator={false}
                      style={{}}
                    />
                  </View>
                )}
              </View>
            )}
          </ScrollView>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text
              style={{
                color: "white",
                fontSize: 16,
                fontFamily: "EstedadRegular",
              }}
            >
              فهمیدم
            </Text>
            {/* <MaterialCommunityIcons name="close" size={24} color="white" /> */}
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  refinementItem: {
    // backgroundColor: "#F8F9FA",
    borderRadius: 12,
    padding: 10,
    marginVertical: 0,
  },
  refinementText: {
    fontSize: 16,
    color: "#2C3E50",
    lineHeight: 24,
    fontFamily: "EstedadRegular",
  },
  separator: {
    height: 1,
    backgroundColor: "#E0E0E0",
    marginVertical: 5,
  },
  centeredView: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalView: {
    backgroundColor: "white",
    borderRadius: 20,
    padding: 20,
    width: "90%",
    maxHeight: "80%",
  },
  tabContainer: {
    flexDirection: "row",
    marginBottom: 20,
    borderRadius: 15,
    backgroundColor: "#F0F0F0",
    padding: 5,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
    borderRadius: 10,
  },
  activeTab: {
    backgroundColor: "white",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tabText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "500",
    fontFamily: "EstedadRegular",
  },
  activeTabText: {
    color: "#007AFF",
  },
  content: {
    // flex: 1,
  },
  errorContainer: {
    backgroundColor: "#FFE5E5",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    flexDirection: "row",
    alignItems: "center",
  },
  errorIconContainer: {
    marginRight: 10,
  },
  errorText: {
    flex: 1,
    color: "#FF0000",
    fontSize: 16,
  },
  correctionContainer: {
    backgroundColor: "#E8F5E9",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    flexDirection: "row",
    alignItems: "center",
  },
  correctionIconContainer: {
    marginRight: 10,
  },
  correctionText: {
    flex: 1,
    color: "#4CAF50",
    fontSize: 16,
  },
  reasonContainer: {
    backgroundColor: "#F5F5F5",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
  },
  reasonLabel: {
    fontSize: 16,
    marginBottom: 10,
    color: "#333",
    fontFamily: "EstedadBold",
  },
  reasonText: {
    color: "#666",
    fontSize: 15,
    fontFamily: "EstedadRegular",
    textAlign: "right",
    writingDirection: "rtl",
    alignSelf: "flex-start",

    // direction: "rtl",
  },
  errorMessage: {
    color: "#FF0000",
    fontSize: 15,
  },
  teachButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    backgroundColor: "#F0F0F0",
    borderRadius: 10,
    padding: 12,
    marginTop: 10,
  },
  teachButtonText: {
    color: "#007AFF",
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  },
  refineContainer: {
    padding: 15,
  },
  closeButton: {
    backgroundColor: "#2196F3",
    borderRadius: 10,
    padding: 12,
    alignItems: "center",
    marginTop: 15,
  },
});

export default GrammarPopup;
