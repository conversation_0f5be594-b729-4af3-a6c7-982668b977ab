import styles from "../../styles/chatStyle";
import ProgressBar from "../progress/ProgressBar";
import InputComponent from "../chat/InputComponent";
import ConfirmationModal from "./ConfirmationModal";
import { useAppDispatch, useAppSelector } from "../../store/hooks";
import { initNewChat } from "../../store/slices/modelSlice";

import {
  View,
  FlatList,
  Keyboard,
  DeviceEventEmitter,
  Modal,
  Text,
  TouchableOpacity,
} from "react-native";
import ChatItem from "./chatItem";
import { useEffect, useRef, useState, useCallback, useMemo } from "react";
import constants from "../../utils/constants";
import SuccessMessageBar from "./SuccessMessageBar";

interface TopicItem {
  _id: string;
  title: string;
  scenario: string;
}

interface TranscriptionItem {
  key: number | string;
  type: "req" | "res" | "loading";
  text: string;
  isTopic: boolean;
  _id: string;
  correctedText?: string;
}

interface ChatItemProps {
  type: string;
  text: string;
  correctedText?: string;
  _id: string;
}

interface ChatUiProps {
  transcriptionResult: TranscriptionItem[];
  Speech: any;
  setIsSpeaking: (index: number | null) => void;
  isSpeaking: boolean | null;
  submit: () => void;
  inputValue: string;
  setInputValue: (value: string) => void;
  isModalVisible: boolean;
  setIsModalVisible: (visible: boolean) => void;
  chatId: string;
  playSpeech: boolean;
  topicItem: TopicItem;
  textSubmitted: boolean;
  chatFinished: boolean;
  setChatFinished: (finished: boolean) => void;
  newChat: boolean;
  scores: any;
  setScores: (scores: any) => void;
  hasReachedDailyLimit: boolean;
  navigation: any;
  recordingComplete: () => void;
  approvedNewChat: boolean;
  setApprovedNewChat: (approved: boolean) => void;
}

export default function ChatUi({
  transcriptionResult,
  Speech,
  setIsSpeaking,
  isSpeaking,
  submit,
  inputValue,
  setInputValue,
  isModalVisible,
  setIsModalVisible,
  chatId,
  playSpeech,
  topicItem,
  textSubmitted,
  chatFinished,
  setChatFinished,
  newChat,
  scores,
  setScores,
  hasReachedDailyLimit,
  navigation,
  recordingComplete,
  approvedNewChat,
  setApprovedNewChat,
}: ChatUiProps) {
  const flatListRef = useRef<FlatList<TranscriptionItem>>(null);
  const [inputHeight, setInputHeight] = useState(60);
  const [reward, setReward] = useState(0);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [showNewChatConfirm, setShowNewChatConfirm] = useState(false);
  const [pendingNewChat, setPendingNewChat] = useState(false);
  const dispatch = useAppDispatch();

  // Intercept newChat trigger to show confirmation
  useEffect(() => {
    if (newChat && !pendingNewChat) {
      setShowNewChatConfirm(true);
      setPendingNewChat(true);
    }
    // Reset pending flag if newChat is reset
    if (!newChat && pendingNewChat) {
      setPendingNewChat(false);
    }
  }, [newChat]);

  // Handler for approve/decline
  const handleApproveNewChat = () => {
    setShowNewChatConfirm(false);
    setApprovedNewChat(true);
    dispatch(initNewChat({ newChat: false })); // reset newChat trigger
  };
  const handleDeclineNewChat = () => {
    setShowNewChatConfirm(false);
    setApprovedNewChat(false);
    dispatch(initNewChat({ newChat: false }));
  };

  // useEffect(() => {
  //   if (approvedNewChat) {
  //     // Only start new chat if approved
  //     // You may want to call a prop or trigger navigation here if needed
  //     // For now, just reset approvedNewChat after use
  //     // dispatch(setApprovedNewChat({ approvedNewChat: false }));
  //     // dispatch(initNewChat({ newChat: true }));
  //   }
  // }, [approvedNewChat]);

  const ScrollEnd = () => {
    if (transcriptionResult.length > 1 && flatListRef.current) {
      // flatListRef.current.scrollToEnd({ animated: true });
      // flatListRef.current.scrollToOffset({
      //   offset: transcriptionResult.length * 300, // Approximate height per message
      //   // animated: true,
      // });
      flatListRef.current?.scrollToIndex({
        index: transcriptionResult.length - 1,
        animated: true,
      });
    }
  };

  const getItemLayout = (
    _data: ArrayLike<TranscriptionItem> | null | undefined,
    index: number
  ) => {
    const baseLength = 150;
    const suggestionBoxHeight = 100; // Estimated height of the suggestion box
    const length = inputValue ? baseLength + suggestionBoxHeight : baseLength;
    const offset = length * index;
    // console.log(`getItemLayout: index=${index}, length=${length}, offset=${offset}, inputValue=${inputValue}`); // Debugging
    return {
      length,
      offset,
      index,
    };
  };

  const handleScrollToIndexFailed = (_info: { index: number }) => {
    const wait = new Promise((resolve) => setTimeout(resolve, 500));
    wait.then(() => {
      ScrollEnd();
    });
  };

  // useEffect(() => {
  //   const keyboardDidShowListener = Keyboard.addListener(
  //     "keyboardDidShow",
  //     () => {
  //       setTimeout(
  //         () => flatListRef.current?.scrollToEnd({ animated: true }),
  //         200
  //       );
  //     }
  //   );

  //   const keyboardDidHideListener = Keyboard.addListener(
  //     "keyboardDidHide",
  //     () => {
  //       setTimeout(
  //         () => flatListRef.current?.scrollToEnd({ animated: true }),
  //         200
  //       );
  //     }
  //   );

  //   return () => {
  //     keyboardDidShowListener.remove();
  //     keyboardDidHideListener.remove();
  //   };
  // }, []);

  // useEffect(() => {
  //   if (transcriptionResult.length > 0) {
  //     setTimeout(() => {
  //       ScrollEnd();

  //     }, 500);
  //   }
  // }, [transcriptionResult]);

  const userMessagesCount = useMemo(
    () =>
      transcriptionResult.filter((item) => item.type === "req" && !item.isTopic)
        .length,
    [transcriptionResult]
  );

  const progress = userMessagesCount / constants.numberOfMessagesForAssessment;
  const lastFortyChats = useMemo(
    () => transcriptionResult.slice(-40),
    [transcriptionResult]
  );
  const reversed = [...lastFortyChats].reverse();
  const renderItem = useCallback(
    ({ item, index }: { item: TranscriptionItem; index: number }) => {
      if (!item.isTopic) {
        const previousItem = reversed[index + 1];
        const nextItem = index > 0 ? reversed[index - 1] : null;
        return (
          <ChatItem
            Speech={Speech}
            setIsSpeaking={setIsSpeaking}
            isSpeaking={isSpeaking}
            item={item}
            previousItem={previousItem}
            nextItem={nextItem}
            chatId={chatId}
            isLastMessage={index === 0}
            index={index}
            playSpeech={playSpeech}
            flatListRef={flatListRef}
            lastIndex={0}
            textSubmitted={textSubmitted}
            chatFinished={chatFinished}
            ScrollEnd={ScrollEnd}
            reward={reward}
            setReward={setReward}
            setShowSuccessMessage={setShowSuccessMessage}
          />
        );
      }
      return null;
    },
    [
      lastFortyChats,
      Speech,
      setIsSpeaking,
      isSpeaking,
      chatId,
      playSpeech,
      textSubmitted,
      chatFinished,
    ]
  );
  return (
    <View style={{ flex: 1, backgroundColor: "#f4f7fe" }}>
      <ConfirmationModal
        visible={showNewChatConfirm}
        onApprove={handleApproveNewChat}
        onDecline={handleDeclineNewChat}
      />
      {topicItem.title != "free talk" && progress > 0 && (
        <ProgressBar progress={progress} />
      )}
      {/* {showSuccessMessage && (
        <SuccessMessageBar
          message="Great job!"
          points={reward}
          onHide={() => setShowSuccessMessage(false)}
        />
      )} */}
      <View style={styles.container}>
        <FlatList
          ref={flatListRef}
          data={reversed}
          style={{
            marginBottom: 10,
          }}
          getItemLayout={getItemLayout}
          // onScrollToIndexFailed={handleScrollToIndexFailed}
          keyExtractor={(item) => item._id || item.key.toString()}
          keyboardShouldPersistTaps="handled"
          renderItem={renderItem}
          showsVerticalScrollIndicator={false}
          inverted={true}
          // onContentSizeChange={handleContentSizeChange}
        />
        <InputComponent
          inputValue={inputValue}
          inputHeight={inputHeight}
          setInputHeight={setInputHeight}
          setInputValue={setInputValue}
          Speech={Speech}
          setIsSpeaking={setIsSpeaking}
          submit={submit}
          scores={scores}
          topicItem={topicItem}
          hasReachedDailyLimit={hasReachedDailyLimit}
          navigation={navigation}
          recordingComplete={recordingComplete}
          approvedNewChat={approvedNewChat}
        />
        {/* Assessment modal removed - now using Assessment screen */}
      </View>
    </View>
  );
}
