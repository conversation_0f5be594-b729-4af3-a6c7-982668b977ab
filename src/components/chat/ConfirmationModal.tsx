import React from "react";
import { Modal, View, Text, TouchableOpacity } from "react-native";

interface ConfirmationModalProps {
  visible: boolean;
  onApprove: () => void;
  onDecline: () => void;
}

const ConfirmationModal: React.FC<ConfirmationModalProps> = ({ visible, onApprove, onDecline }) => (
  <Modal
    visible={visible}
    transparent
    animationType="fade"
    onRequestClose={onDecline}
  >
    <View style={{ flex: 1, backgroundColor: "rgba(0,0,0,0.4)", justifyContent: "center", alignItems: "center" }}>
      <View style={{ backgroundColor: "white", borderRadius: 16, padding: 24, width: 300, alignItems: "center" }}>
        <Text style={{ fontSize: 16, fontFamily: "EstedadRegular", marginBottom: 24, textAlign: "center" }}>
          آیا مطمئن هستید که می‌خواهید چت جدید شروع کنید؟
        </Text>
        <View style={{ flexDirection: "row-reverse", justifyContent: "center", width: "100%" }}>
          <TouchableOpacity
            style={{ backgroundColor: "#4CAF50", paddingVertical: 10, paddingHorizontal: 24, borderRadius: 8, marginLeft: 8 }}
            onPress={onApprove}
          >
            <Text style={{ color: "white", fontFamily: "EstedadRegular", fontSize: 16 }}>تایید</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={{ backgroundColor: "#f44336", paddingVertical: 10, paddingHorizontal: 24, borderRadius: 8 }}
            onPress={onDecline}
          >
            <Text style={{ color: "white", fontFamily: "EstedadRegular", fontSize: 16 }}>انصراف</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  </Modal>
);

export default ConfirmationModal;
