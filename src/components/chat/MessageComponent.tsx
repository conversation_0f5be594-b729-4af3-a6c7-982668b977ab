import React, { useEffect, useState } from "react";
import { View, StyleSheet, Text, Dimensions } from "react-native";
import LoadingDots from "react-native-loading-dots";
import { SelectableText } from "@armata99/react-native-selectable-text";
import handleScelection from "../../utils/handleScelection";
import { compareTexts, TextSegment } from "../../utils/compareTexts";
import { TouchableOpacity } from "react-native";

interface MessageComponentProps {
  item: any;
  previousItem: any | null;
  nextItem: any | null;
  isSpeaking: boolean;
  isGrammarCorrect?: boolean | null;
  showOriginalText?: boolean;
  translatedText?: string;
  Icons: any;
  setGrammarModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
  correctedText?: string;
  ScrollEnd: () => void;
}

interface TranslationTextProps {
  translatedText?: string;
  showOriginalText?: boolean;
}

interface LoadingUiProps {
  loadingReq: boolean;
}

const LoadingUi: React.FC<LoadingUiProps> = ({ loadingReq }) => {
  let colors = ["#f2f2f5", "#ffffff", "#f2f2f5"];
  if (loadingReq) {
    colors = ["#5480d1", "#5480d1", "#5480d1"];
  }

  return (
    <View style={styles.loadingScreen}>
      <View style={styles.dotsWrapper}>
        <LoadingDots
          size={12}
          bounceHeight={5}
          dots={3}
          colors={colors}
          gap={2}
        />
      </View>
    </View>
  );
};

const TranslationText: React.FC<TranslationTextProps> = ({
  translatedText,
  showOriginalText,
}) => {
  if (showOriginalText || !translatedText) return null;
  return (
    <Text
      style={{
        textAlign: "right",
        fontSize: 15,
        color: "#ededed",
        fontFamily: "EstedadRegular",
        marginBottom: 6,
      }}
    >
      {translatedText}
    </Text>
  );
};

// Define props interface for GrammarCorrectionText
interface GrammarCorrectionTextProps {
  isGrammarCorrect?: boolean | null;
  correctedText?: string;
  originalText?: string;
  ScrollEnd: () => void;
}

// New component to display grammar corrections with highlighted differences
const GrammarCorrectionText: React.FC<GrammarCorrectionTextProps> = ({
  isGrammarCorrect,
  correctedText,
  originalText,
  ScrollEnd,
}) => {
  // Check if grammar is correct, or if correctedText is missing
  if (isGrammarCorrect || !correctedText) {
    return null;
  }

  // Compare the original text with the corrected text
  const segments = compareTexts(originalText || "", correctedText);

  return (
    <View
      style={{
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "flex-start",
        marginBottom: 6,
        paddingTop: 4,
      }}
      onLayout={() => {
        // Use requestAnimationFrame to ensure the layout is complete before scrolling
        requestAnimationFrame(() => {
          // This will trigger a re-render and update scroll position
          // ScrollEnd();
        });
      }}
    >
      {segments.map((segment, index) => (
        <Text
          key={index}
          style={{
            fontSize: 16,
            color: segment.isDifferent ? "green" : "gray",
            fontWeight: segment.isDifferent ? "bold" : "normal",
          }}
        >
          {segment.text}
          {index < segments.length - 1 ? " " : ""}
        </Text>
      ))}
    </View>
  );
};

interface DynamicTagProps {
  children: React.ReactNode;
  style?: any;
  type?: string;
  isGrammarCorrect?: boolean | null;
  correctedText?: string;
  onPress?: () => void;
}

const DynamicTag: React.FC<DynamicTagProps> = ({
  children,
  style,
  type,
  isGrammarCorrect,
  correctedText,
  onPress,
  ...props
}) => {
  if (type === "req" && !isGrammarCorrect && correctedText) {
    return (
      <TouchableOpacity onPress={onPress} style={style} {...props}>
        {children}
      </TouchableOpacity>
    );
  }
  return (
    <View style={style} {...props}>
      {children}
    </View>
  );
};

const MessageComponent = ({
  item,
  previousItem,
  nextItem,
  isSpeaking,
  isGrammarCorrect,
  showOriginalText,
  translatedText,
  Icons,
  setGrammarModalVisible,
  correctedText,
  ScrollEnd,
}: MessageComponentProps) => {
  let marginTopValue = 30;
  if (!previousItem || previousItem.isTopic) {
    marginTopValue = 30;
  } else if (previousItem && previousItem.type === item.type) {
    marginTopValue = 0;
  } else if (
    previousItem &&
    previousItem.type === "res" &&
    item.type === "loading"
  ) {
    marginTopValue = 0;
  } else {
    marginTopValue = 30;
  }

  const screenWidth = Dimensions.get("window").width;

  return (
    <DynamicTag
      type={item.type}
      isGrammarCorrect={isGrammarCorrect}
      correctedText={correctedText}
      onPress={() => setGrammarModalVisible(true)}
      style={[
        {
          backgroundColor:
            item.type === "req" || item.type === "loadingReq"
              ? "#fff"
              : "#2a72e8",
          elevation: 2,
          shadowColor: "#000",
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.2,
          shadowRadius: 1.41,
          marginRight: item.type === "res" ? 40 : 0,
          marginTop: marginTopValue,
          borderTopLeftRadius: item.type === "req" ? 15 : 15,
          borderTopRightRadius: item.type === "req" ? 15 : 15,
          borderBottomLeftRadius: item.type === "req" ? 15 : 0,
          borderBottomRightRadius: item.type === "req" ? 0 : 15,
        },
        styles.item,
      ]}
    >
      {item.type === "loading" ||
      item.type === "loadingReq" ||
      item.type === "loadingRes" ? (
        <LoadingUi loadingReq={item.type === "loadingReq"} />
      ) : (
        <View
          style={{
            // maxWidth: getMaxWidth(item.type),
            // minWidth:
            //   item.type === "res" ? "50%" : correctedText ? "40%" : "30%",
            minWidth:
              item.type === "res"
                ? screenWidth * 0.4
                : correctedText
                ? screenWidth * 0.2
                : screenWidth * 0.1,
            maxWidth: item.type === "req" ? screenWidth * 0.6 : undefined,
          }}
        >
          <View
            style={{
              marginVertical: 5,

              ...((item.type !== "req" &&
                !showOriginalText &&
                translatedText) ||
              (item.type === "req" && !isGrammarCorrect && correctedText)
                ? {
                    borderBottomWidth: 1,
                    borderBottomColor: "#ccc",
                    paddingBottom: 10,
                  }
                : {}),
            }}
          >
            <Text
              style={[
                {
                  textAlign: "left",
                  fontSize: 17,
                  lineHeight: 24,
                  fontWeight: item.type === "req" ? "bold" : "normal",
                  color: item.type === "res" ? "white" : "#555758",
                },
              ]}
              selectable={true}
            >
              {item.text}
            </Text>
          </View>
          {!showOriginalText && translatedText && (
            <TranslationText
              translatedText={translatedText}
              showOriginalText={showOriginalText}
            />
          )}
          {item.type === "req" && (
            <GrammarCorrectionText
              isGrammarCorrect={isGrammarCorrect}
              correctedText={correctedText}
              originalText={item.text}
              ScrollEnd={ScrollEnd}
            />
          )}
        </View>
      )}
      {item.type === "res" && (
        <View
          style={{
            ...styles.iconContainer,
          }}
        >
          <Icons />
        </View>
      )}
      {/* Modal is no longer needed as we're showing corrections inline */}
    </DynamicTag>
  );
};

const styles = StyleSheet.create({
  loadingScreen: {
    paddingVertical: 10,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 7,
  },
  item: {
    paddingHorizontal: 14,
    paddingVertical: 10,
    maxWidth: "90%",
    marginBottom: 5,
  },
  iconContainer: {
    marginTop: 15,
  },
  dotsWrapper: {
    flexDirection: "row",
    alignItems: "center",
  },
});

export default MessageComponent;
