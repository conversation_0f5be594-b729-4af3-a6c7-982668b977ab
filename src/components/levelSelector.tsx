import React, { useRef, useState } from "react";
import { Pressable, StyleSheet, Text, View } from "react-native";
import { Dropdown } from "react-native-element-dropdown";
import AntDesign from "@expo/vector-icons/AntDesign";
import { useAppSelector } from "../store/hooks";
import { useDispatch } from "react-redux";
import { setLevel } from "../store/slices/authSlice";
import RequestHelper from "../utils/requestHelper";

const data = [
  { label: "Beginner", value: "beginner" },
  { label: "Intermediate", value: "intermediate" },
  { label: "Advanced", value: "advanced" },
];

const DropdownComponent = () => {
  const { level } = useAppSelector((state) => state.auth);
  const [value, setValue] = useState(level);
  const [isFocus, setIsFocus] = useState(false);
  const dispatch = useDispatch();
  const dropdownRef = useRef(null);

  const handleOpenDropdown = () => {
    if (dropdownRef.current) {
      dropdownRef.current.open();
    }
  };

  const renderLabel = () => {
    if (value || isFocus) {
      return (
        <Text style={[styles.label, isFocus && { color: "blue" }]}>Level</Text>
      );
    }
    return null;
  };

  return (
    <View style={styles.container}>
      {renderLabel()}
      <Pressable
        onPress={() => {
          handleOpenDropdown();
        }}
        hitSlop={30}
      >
        <Dropdown
          ref={dropdownRef}
          style={[styles.dropdown, isFocus && { borderColor: "blue" }]}
          placeholderStyle={styles.placeholderStyle}
          selectedTextStyle={styles.selectedTextStyle}
          inputSearchStyle={styles.inputSearchStyle}
          iconStyle={styles.iconStyle}
          data={data}
          maxHeight={300}
          labelField="label"
          valueField="value"
          // placeholder={!isFocus ? "Select item" : "..."}
          value={value}
          onFocus={() => setIsFocus(true)}
          onBlur={() => setIsFocus(false)}
          onChange={(item) => {
            setValue(item.value);
            setIsFocus(false);
            dispatch(setLevel({ level: item.value }));
            RequestHelper("patch", `/users`, {
              level: item.value,
            });
          }}
        />
      </Pressable>
    </View>
  );
};

export default DropdownComponent;

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    paddingVertical: 15,
    maxWidth: 200,
    minWidth: 160,
  },
  dropdown: {
    height: 50,
    borderColor: "#4169E1",
    borderWidth: 0.5,
    borderRadius: 8,
    paddingHorizontal: 8,
  },
  icon: {
    marginRight: 5,
  },
  label: {
    position: "absolute",
    backgroundColor: "white",
    left: 22,
    top: 6,
    zIndex: 999,
    paddingHorizontal: 6,
    fontSize: 14,
    color: "#4169E1",
  },
  placeholderStyle: {
    fontSize: 16,
  },
  selectedTextStyle: {
    fontSize: 16,
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
});
