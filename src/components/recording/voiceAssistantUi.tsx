import React, { useMemo, useRef, useState, useEffect } from "react";
import { View, Text, TouchableOpacity, Image, Animated } from "react-native";
import * as Speech from "expo-speech";
import MP3Recording from "./MP3Recording";
import { LinearGradient } from "expo-linear-gradient";
import { Ionicons } from "@expo/vector-icons";
import styles from "../../styles/voiceAssistantStyle";
import GrammarPopup from "../chat/GrammarPopup";
import { compareTexts } from "../../utils/compareTexts";
import AudioVisualizer from "./AudioVisualizer";
import SettingsModal from "../modals/SettingsModal";
import ThinkingAnimation from "./ThinkingAnimation";
import RecordingAnimation from "./RecordingAnimation";
import AnimatedText from "./AnimatedText";
import { useNavigation } from "@react-navigation/native";
import { useAppSelector } from "../../store/hooks";
import DailyLimitModal from "../modals/DailyLimitModal";
import {
  recordingComplete,
  selectVoice,
  useVoiceAssistantLogic,
} from "./voiceAssistantHelpers";

const VoiceAssistant = ({
  transcriptionResult,
  setTranscriptionResult,
  chatId,
  isSpeaking,
  setIsSpeaking,
  setTextSubmitted,
  scenarioId,
}: any) => {
  const { isPremium } = useAppSelector((state) => state.auth);
  const [grammarErrorSpeechText, setGrammarErrorSpeechText] = useState<{
    text: string;
    color: string;
  }[] | null>(null);
  const [shouldAutoRestart, setShouldAutoRestart] = useState(false);
  const [shouldRestartAfterGrammarError, setShouldRestartAfterGrammarError] =
    useState(false);
  const [lastResponseLength, setLastResponseLength] = useState(0);
  const [audioLevel, setAudioLevel] = useState<number>(-160); // Default to minimum level
  const [isRecording, setIsRecording] = useState<boolean>(false);
  const [isVoiceModalVisible, setIsVoiceModalVisible] = useState(false);
  const [isDailyLimitModalVisible, setIsDailyLimitModalVisible] = useState(false);
  const [voices, setVoices] = useState<any[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string | null>(null);
  const [translation, setTranslation] = useState<string>("");
  const [isTranslating, setIsTranslating] = useState<boolean>(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const mp3RecordingRef = useRef<any>(null);
  const speechTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const previousIsSpeakingRef = useRef<number | null>(null);
  let lastItem = transcriptionResult[transcriptionResult.length - 1];
  const lastUserMessage = useMemo(
    () => [...transcriptionResult].reverse().find((i: any) => i.type === "req"),
    [transcriptionResult]
  );
  const lastAssistantMessage = useMemo(
    () =>
      [...transcriptionResult]
        .reverse()
        .find((i: any) => i.type === "res" && !!i.text),
    [transcriptionResult]
  );

  // Grammar states
  const [isGrammarCorrect, setIsGrammarCorrect] = useState<boolean | null>(
    null
  );
  const [correctedText, setCorrectedText] = useState<string>("");
  const [grammarModalVisible, setGrammarModalVisible] = useState(false);
  const lastCheckedTextRef = useRef<string>("");

  useVoiceAssistantLogic(
    transcriptionResult,
    setTranscriptionResult,
    chatId,
    isSpeaking,
    setIsSpeaking,
    setTextSubmitted,
    lastResponseLength,
    setLastResponseLength,
    shouldAutoRestart,
    setShouldAutoRestart,
    shouldRestartAfterGrammarError,
    setShouldRestartAfterGrammarError,
    mp3RecordingRef,
    speechTimeoutRef,
    previousIsSpeakingRef,
    lastUserMessage,
    lastAssistantMessage,
    setIsGrammarCorrect,
    setCorrectedText,
    lastCheckedTextRef,
    setIsTranslating,
    setTranslation,
    setIsRecording,
    setVoices,
    setSelectedVoice
  );

  const navigation = useNavigation();

  useEffect(() => {
    return () => {
      Speech.stop();
    };
  }, []);

  useEffect(() => {
    const unsubscribe = navigation.addListener("blur", () => {
      Speech.stop();
    });

    return unsubscribe;
  }, [navigation]);

  // Fade in animation for new content
  useEffect(() => {
    if (lastAssistantMessage || translation || correctedText) {
      fadeAnim.setValue(0);
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }
  }, [lastAssistantMessage?.text, translation, correctedText, fadeAnim]);

  return (
    <LinearGradient
      colors={["#0a1535", "#1a2a5e", "#0a1535"]}
      style={styles.container}
    >
      {/* Avatar Container */}
      {/* <View style={styles.avatarContainer}>
        <View style={styles.avatarCircle}>
          <Image source={avatarSource} style={styles.avatar} />
        </View>
      </View> */}

      {/* Response Text */}
      <View style={styles.textContainer}>
        {grammarErrorSpeechText ? (
          <Animated.View style={{ opacity: fadeAnim }}>
            <View style={styles.errorText}>
              <View style={{ flexDirection: 'row', flexWrap: 'wrap' }}>
                {grammarErrorSpeechText.map((segment, index) => (
                  <Text
                    key={index}
                    style={[
                      styles.responseText,
                      { color: segment.color },
                    ]}
                  >
                    {segment.text}
                  </Text>
                ))}
              </View>
            </View>
          </Animated.View>
        ) : (
          <>
            {(lastItem?.type === "loading" ||
              lastItem?.type === "loadingReq") && <ThinkingAnimation />}

            {lastItem?.type === "res" && lastAssistantMessage && (
              <View
                style={{
                  // opacity: fadeAnim,
                  //  marginTop: isRecording ? 50 : 0
                }}
              >
                {/* <AnimatedText
                  text={lastAssistantMessage.text}
                  style={styles.responseText}
                  speed={25}
                /> */}
                <Text style={styles.responseText}>{lastAssistantMessage.text}</Text>
                {translation && (
                  <View style={styles.translationContainer}>
                    {/* <Text style={styles.translationLabel}>ترجمه فارسی</Text> */}
                    <Text style={styles.translationText}>{translation}</Text>
                  </View>
                )}
                {/* {isTranslating && (
                  <View style={styles.translationContainer}>
                    <Text style={styles.translationLabel}>در حال ترجمه...</Text>
                    <View
                      style={{
                        flexDirection: "row",
                        justifyContent: "center",
                        marginTop: 8,
                      }}
                    >
                      <Text
                        style={[
                          styles.translationText,
                          { fontSize: 14, opacity: 0.6 },
                        ]}
                      >
                        ●●●
                      </Text>
                    </View>
                  </View>
                )} */}
              </View>
            )}

            {/* Inline grammar corrections for the last user message */}
            {lastItem?.type === "res" &&
              lastUserMessage?.text &&
              !isGrammarCorrect &&
              !!correctedText && (
                <Animated.View style={{ opacity: fadeAnim }}>
                  <TouchableOpacity
                    onPress={() => setGrammarModalVisible(true)}
                  >
                    <View style={styles.grammarCorrectionContainer}>
                      <Text style={styles.grammarCorrectionLabel}>
                        پیشنهاد تصحیح گرامری (برای مشاهده جزئیات ضربه بزنید)
                      </Text>
                      <View style={{ flexDirection: "row", flexWrap: "wrap" }}>
                        {compareTexts(lastUserMessage.text, correctedText).map(
                          (segment, index, arr) => (
                            <Text
                              key={index}
                              style={
                                segment.isDifferent
                                  ? styles.grammarCorrectedWord
                                  : styles.grammarOriginalWord
                              }
                            >
                              {segment.text}
                              {index < arr.length - 1 ? " " : ""}
                            </Text>
                          )
                        )}
                      </View>
                    </View>
                  </TouchableOpacity>
                </Animated.View>
              )}
          </>
        )}

        {/* Show recording animation when user is speaking or when processing user input */}
        {(isRecording || lastItem?.type === "req") && (
          <RecordingAnimation
            audioLevel={audioLevel}
            isRecording={isRecording || lastItem?.type === "req"}
          />
        )}
      </View>

      {/* Recording Animation - Alternative placement if needed */}
      {/* <RecordingAnimation audioLevel={audioLevel} isRecording={isRecording} /> */}

      {/* Control Buttons */}
      <View style={styles.controlsContainer}>
        {/* Record Button using MP3Recording */}
        <View style={{ alignItems: "center", display: "none" }}>
          <MP3Recording
            ref={mp3RecordingRef}
            isSpeaking={isSpeaking}
            setIsSpeaking={setIsSpeaking}
            Speech={Speech}
            onRecordingComplete={async (uri: string) => {
              setGrammarErrorSpeechText(null); // Clear previous error speech when a new recording starts
              await recordingComplete(
                transcriptionResult,
                setTranscriptionResult,
                chatId,
                uri,
                setTextSubmitted,
                isGrammarCorrect,
                setGrammarErrorSpeechText,
                mp3RecordingRef,
                setShouldRestartAfterGrammarError,
                setIsSpeaking,
                setIsDailyLimitModalVisible,
                scenarioId,
                isPremium
              );
            }}
            autoStart={true}
            transcriptionResult={transcriptionResult}
            onAudioLevelChange={setAudioLevel}
          />
        </View>

        <TouchableOpacity
          style={styles.controlButton}
          onPress={() => setIsVoiceModalVisible(true)}
        >
          <Ionicons name="settings-outline" size={30} color="#fff" />
        </TouchableOpacity>
        <TouchableOpacity
          style={styles.hangupButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="call-outline" size={30} color="#fff" />
        </TouchableOpacity>
      </View>

      {/* Grammar details modal */}
      {lastUserMessage?.text && !!correctedText && (
        <GrammarPopup
          visible={grammarModalVisible}
          originalText={lastUserMessage.text}
          correctedText={correctedText}
          onClose={() => setGrammarModalVisible(false)}
        />
      )}

      <SettingsModal
        visible={isVoiceModalVisible}
        onClose={() => setIsVoiceModalVisible(false)}
        onSelectVoice={(voiceId: string) =>
          selectVoice(voiceId, setSelectedVoice, setIsVoiceModalVisible)
        }
        selectedVoice={selectedVoice}
        voices={voices}
        onStopRecording={() => {
          if (mp3RecordingRef.current && mp3RecordingRef.current.stopRecording) {
            mp3RecordingRef.current.stopRecording();
          }
        }}
        onStartRecording={() => {
          if (mp3RecordingRef.current && mp3RecordingRef.current.restartRecording) {
            mp3RecordingRef.current.restartRecording();
          }
        }}
        isSpeaking={isSpeaking}
      />
      
      <DailyLimitModal
        visible={isDailyLimitModalVisible}
        onClose={() => setIsDailyLimitModalVisible(false)}
        onSubscribe={() => {
          setIsDailyLimitModalVisible(false);
          // @ts-ignore
          navigation.navigate("profileTab", { screen: "subscription" });
        }}
      />
    </LinearGradient>
  );
};

export default VoiceAssistant;
