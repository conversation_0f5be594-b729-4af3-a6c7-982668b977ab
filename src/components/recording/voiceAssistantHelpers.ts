import AsyncStorage from "@react-native-async-storage/async-storage";
import { checkGrammar, getTranslation, llmRequest } from "../chat/chatUtils";
import { handleRecordingComplete } from "./handleTranscription";
import { getAvailableVoices } from "../../utils/voiceUtils";
import React, { useEffect, useRef } from "react"; // Added useRef
import * as Speech from "expo-speech";
import { stopSpeech, speakWithSelectedVoice } from "../../utils/speechUtils";
import { getHistoryFromTranscription } from "../chat/chatUtils";



export const checkDailyLimit = async (
  isPremium: boolean
): Promise<{ hasReachedLimit: boolean; isPremium: boolean }> => {
  // For premium users, they never reach the limit
  if (isPremium) {
    return { hasReachedLimit: false, isPremium: true };
  }
  
  // For non-premium users, check AsyncStorage for daily limit
  try {
    const limitData = await AsyncStorage.getItem("dailyLimitData");
    if (limitData) {
      const { timestamp } = JSON.parse(limitData);
      const currentTime = new Date().getTime();
      const threeHoursInMs = 3 * 60 * 60 * 1000;

      // Check if 3 hours have passed since the limit was reached
      if (currentTime - timestamp < threeHoursInMs) {
        return { hasReachedLimit: true, isPremium: false };
      } else {
        // Reset the limit if 3 hours have passed
        await AsyncStorage.removeItem("dailyLimitData");
      }
    }
  } catch (error) {
    console.log("Error checking daily limit:", error);
  }
  
  // If we get here, the user hasn't reached the limit
  return { hasReachedLimit: false, isPremium: false };
};

export const recordingComplete = async (
  transcriptionResult: any,
  setTranscriptionResult: any,
  chatId: any,
  uri: string,
  setTextSubmitted: any,
  isGrammarCorrect: boolean | null,
  setGrammarErrorSpeechText: (input: {text: string, color: string}[] | null) => void,
  mp3RecordingRef: any,
  setShouldRestartAfterGrammarError: (should: boolean) => void,
  setIsSpeaking: (speaking: boolean | null) => void,
  setDailyLimitModalVisible?: (visible: boolean) => void,
  scenarioId?: string,
  isPremium?: boolean
) => {
  // Check daily limit before processing
  const { hasReachedLimit, isPremium: userIsPremium } = await checkDailyLimit(isPremium || false);
  
  if (hasReachedLimit && !userIsPremium) {
    // Show daily limit modal for non-premium users
    if (setDailyLimitModalVisible) {
      setDailyLimitModalVisible(true);
    }
    return; // Don't process the recording
  }
  
  await handleRecordingComplete(
    transcriptionResult,
    setTranscriptionResult,
    chatId,
    uri,
    setTextSubmitted,
    isGrammarCorrect,
    setGrammarErrorSpeechText,
    mp3RecordingRef,
    setShouldRestartAfterGrammarError,
    setIsSpeaking,
    true
  );
};

export const selectVoice = async (voiceId: string, setSelectedVoice: (voiceId: string | null) => void, setIsVoiceModalVisible: (visible: boolean) => void) => {
  try {
    await AsyncStorage.setItem("selectedVoice", voiceId);
    setSelectedVoice(voiceId);
    setIsVoiceModalVisible(false);
  } catch (error) {
    console.error("Error saving selected voice:", error);
  }
};

export const useVoiceAssistantLogic = (
  transcriptionResult: any,
  setTranscriptionResult: any,
  chatId: any,
  isSpeaking: any,
  setIsSpeaking: any,
  setTextSubmitted: any,
  lastResponseLength: number,
  setLastResponseLength: (length: number) => void,
  shouldAutoRestart: boolean,
  setShouldAutoRestart: (should: boolean) => void,
  shouldRestartAfterGrammarError: boolean,
  setShouldRestartAfterGrammarError: (should: boolean) => void,
  mp3RecordingRef: any,
  speechTimeoutRef: any,
  previousIsSpeakingRef: any,
  lastUserMessage: any,
  lastAssistantMessage: any,
  setIsGrammarCorrect: (correct: boolean | null) => void,
  setCorrectedText: (text: string) => void,
  lastCheckedTextRef: any,
  setIsTranslating: (translating: boolean) => void,
  setTranslation: (translation: string) => void,
  setIsRecording: (recording: boolean) => void,
  setVoices: (voices: any[]) => void,
  setSelectedVoice: (voiceId: string | null) => void,
) => {
  const silenceTimeoutRef = useRef<NodeJS.Timeout | null>(null); // Added silenceTimeoutRef

  // Monitor for new LLM responses to prepare for auto-restart
  useEffect(() => {
    if (transcriptionResult.length > lastResponseLength) {
      const lastItem = transcriptionResult[transcriptionResult.length - 1];

      if (lastItem?.type === "res" && lastItem.text) {
        setShouldAutoRestart(true);
        setLastResponseLength(transcriptionResult.length);
      }
    }
  }, [transcriptionResult, lastResponseLength, setLastResponseLength, setShouldAutoRestart]);

  // Check grammar when the last user message changes
  // useEffect(() => {
  //   if (
  //     lastUserMessage?.text &&
  //     lastUserMessage.text !== lastCheckedTextRef.current
  //   ) {
  //     console.log("checking grammar in file voiceAssistantHelpers.ts");
      
  //     lastCheckedTextRef.current = lastUserMessage.text;
  //     setIsGrammarCorrect(null);
  //     setCorrectedText("");
  //     checkGrammar(lastUserMessage.text)
  //       .then((result) => {
  //         if (result?.correct) {
  //           setIsGrammarCorrect(true);
  //           setCorrectedText("");
  //         } else {
  //           setIsGrammarCorrect(false);
  //           const corrected = result?.corrected || "";
  //           setCorrectedText(corrected);
            
  //           // Speak the grammar correction with speed 1.0 and ask user to correct it
  //           const originalText = lastUserMessage.text;
  //           // const correctionMessage = `You said: "${originalText}". Please correct this sentence. The suggested correction is: "${corrected}".`;
  //           //before
  //           // playSound(require('../../../assets/tts/before.mp3'));
  //           // speakWithSelectedVoice(originalText, { rate: 1.0}, setIsSpeaking);
  //           // playSound(require('../../../assets/tts/after.mp3'));
  //           //after
  //           speakWithSelectedVoice(corrected, { rate: 1.0}, setIsSpeaking);
            
  //           // Stop the conversation from auto-restarting
  //           setShouldAutoRestart(false);
  //         }
  //       })
  //       .catch(() => {
  //         setIsGrammarCorrect(null);
  //         setCorrectedText("");
  //       });
  //   }
  // }, [lastUserMessage?.text, setIsGrammarCorrect, setCorrectedText, setShouldAutoRestart]);

  // Automatically translate when lastAssistantMessage changes
  useEffect(() => {
    const translateMessage = async () => {
      setTranslation("");
      if (lastAssistantMessage?.text && lastAssistantMessage.text !== "") {
        const translatedMessages = await AsyncStorage.getItem("translatedMessages");
        const translatedObj = translatedMessages ? JSON.parse(translatedMessages) : {};
        
        if (!translatedObj[lastAssistantMessage.text]) {
          setIsTranslating(true);
          try {
            const translatedText = await getTranslation(lastAssistantMessage.text);
            setTranslation(translatedText);
            
            translatedObj[lastAssistantMessage.text] = translatedText;
            await AsyncStorage.setItem("translatedMessages", JSON.stringify(translatedObj));
          } catch (error) {
            console.error("Translation error:", error);
            setTranslation("Translation failed");
          } finally {
            setIsTranslating(false);
          }
        } else {
          setTranslation(translatedObj[lastAssistantMessage.text]);
        }
      } else {
        setTranslation("");
      }
    };

    translateMessage();
  }, [lastAssistantMessage?.text]);

  // Monitor isSpeaking state to detect when speech synthesis completes
  useEffect(() => {
    if (speechTimeoutRef.current) {
      clearTimeout(speechTimeoutRef.current);
      speechTimeoutRef.current = null;
    }
    if (
      isSpeaking === null &&
      previousIsSpeakingRef.current !== null &&
      (shouldAutoRestart 
        || shouldRestartAfterGrammarError
      )
    ) {
      if (mp3RecordingRef.current) {
        mp3RecordingRef.current.restartRecording();
        setShouldAutoRestart(false);
        setShouldRestartAfterGrammarError(false);
      }
    }

    previousIsSpeakingRef.current = isSpeaking;
  }, [isSpeaking, shouldAutoRestart, shouldRestartAfterGrammarError, mp3RecordingRef, previousIsSpeakingRef, speechTimeoutRef, setShouldAutoRestart, setShouldRestartAfterGrammarError]);

  // Monitor recording state and implement silence detection
  useEffect(() => {
    const updateRecordingState = () => {
      if (mp3RecordingRef.current) {
        const isRecording = mp3RecordingRef.current.isCurrentlyRecording();
        setIsRecording(isRecording);

        if (isRecording) {
          // Reset silence timeout if recording is active
          if (silenceTimeoutRef.current) {
            clearTimeout(silenceTimeoutRef.current);
            silenceTimeoutRef.current = null;
          }
        }
        //  else {
        //   // If not recording, start a timeout to prevent "bullshit" transcriptions
        //   if (!silenceTimeoutRef.current) {
        //     silenceTimeoutRef.current = setTimeout(() => {
        //       // Clear transcriptionResult if silence detected for a while
        //       setTranscriptionResult([]);
        //       console.log("Silence detected, clearing transcription result.");
        //     }, 3000); // 3 seconds of silence
        //   }
        // }
      }
    };

    updateRecordingState();

    const interval = setInterval(updateRecordingState, 100);

    return () => {
      clearInterval(interval);
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
      }
    };
  }, [mp3RecordingRef, setIsRecording, setTranscriptionResult]); // Added setTranscriptionResult to dependencies

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (speechTimeoutRef.current) {
        clearTimeout(speechTimeoutRef.current);
      }
      if (silenceTimeoutRef.current) {
        clearTimeout(silenceTimeoutRef.current);
      }
      stopSpeech();
      if (mp3RecordingRef.current) {
        mp3RecordingRef.current.stopRecording();
      }
    };
  }, [speechTimeoutRef, mp3RecordingRef]);

  useEffect(() => {
    setTranscriptionResult([]);
  }, [chatId, setTranscriptionResult]);

  useEffect(() => {
    const loadVoices = async () => {
      try {
        const availableVoices = await getAvailableVoices();
        setVoices(availableVoices);

        const savedVoice = await AsyncStorage.getItem("selectedVoice");
        if (savedVoice) {
          setSelectedVoice(savedVoice);
        } else if (availableVoices.length > 0) {
          setSelectedVoice(availableVoices[0].id);
        }
      } catch (error) {
        console.error("Error loading voices:", error);
      }
    };

    loadVoices();
  }, [setVoices, setSelectedVoice]);
};
