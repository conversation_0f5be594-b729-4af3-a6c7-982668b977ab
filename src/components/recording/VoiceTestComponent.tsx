import React, { useState, useEffect } from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { getAvailableVoices } from "../../utils/voiceUtils";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { speakWithSelectedVoice } from "../../utils/speechUtils";

const VoiceTestComponent = () => {
  const [voices, setVoices] = useState<any[]>([]);
  const [selectedVoice, setSelectedVoice] = useState<string | null>(null);
  const [testText, setTestText] = useState("This is a test of the voice selection feature.");

  useEffect(() => {
    loadVoices();
  }, []);

  const loadVoices = async () => {
    try {
      const availableVoices = await getAvailableVoices();
      setVoices(availableVoices);
      
      // Get saved voice preference
      const savedVoice = await AsyncStorage.getItem("selectedVoice");
      if (savedVoice) {
        setSelectedVoice(savedVoice);
      } else if (availableVoices.length > 0) {
        // Set first voice as default if none saved
        setSelectedVoice(availableVoices[0].id);
      }
    } catch (error) {
      console.error("Error loading voices:", error);
    }
  };

  const selectVoice = async (voiceId: string) => {
    try {
      await AsyncStorage.setItem("selectedVoice", voiceId);
      setSelectedVoice(voiceId);
    } catch (error) {
      console.error("Error saving selected voice:", error);
    }
  };

  const testVoice = () => {
    speakWithSelectedVoice(testText);
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Voice Selection Test</Text>
      
      <View style={styles.voiceList}>
        {voices.map((voice) => (
          <TouchableOpacity
            key={voice.id}
            onPress={() => selectVoice(voice.id)}
            style={{
              ...styles.voiceButton,
              backgroundColor: selectedVoice === voice.id ? "#4CAF50" : "#f0f0f0",
            }}
          >
            <Text 
              style={{ 
                ...styles.voiceButtonText,
                color: selectedVoice === voice.id ? "white" : "black",
              }}
            >
              {voice.name} ({voice.language})
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      
      <TouchableOpacity
        onPress={testVoice}
        style={styles.testButton}
      >
        <Text style={styles.testButtonText}>Test Voice</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 20,
    backgroundColor: "#f5f5f5",
    borderRadius: 10,
    margin: 20,
  },
  title: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 20,
    textAlign: "center",
  },
  voiceList: {
    marginBottom: 20,
  },
  voiceButton: {
    padding: 10,
    marginVertical: 5,
    borderRadius: 5,
  },
  voiceButtonText: {
    fontSize: 16,
  },
  testButton: {
    backgroundColor: "#2196F3",
    padding: 15,
    borderRadius: 5,
    alignItems: "center",
  },
  testButtonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
  },
});

export default VoiceTestComponent;