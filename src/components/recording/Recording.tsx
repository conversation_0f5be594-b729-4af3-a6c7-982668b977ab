import * as React from "react";
import {
  Text,
  View,
  StyleSheet,
  Button,
  Pressable,
  Image,
  ActivityIndicator,
} from "react-native";
import AudioRecord from "react-native-audio-record";
// import { Buffer } from "buffer";
import { PermissionsAndroid, EmitterSubscription } from "react-native";
// import LoadingDots from "react-native-loading-dots";
import { Feather, FontAwesome, FontAwesome6 } from "@expo/vector-icons";
import wav from "node-wav";
import RNFS from "react-native-fs";
import { Buffer } from "buffer";
import { runModel } from "../../utils/whisperModel";

export default function App({ Speech, setIsSpeaking }) {
  const [recording, setRecording] = React.useState<Boolean>();
  const [transcribing, setTranscribing] = React.useState<Boolean>(false);
  let silenceTimer: NodeJS.Timeout | null = null;
  const [audioData, setAudioData] = React.useState<number[]>([]);
  const [audioDataListener, setAudioDataListener] =
    React.useState<EmitterSubscription | null>(null);
  const silenceThreshold = 0.005; // Increased threshold for more reliable silence detection
  const silenceDuration = 5000; // 2 seconds in milliseconds

  async function requestMicrophonePermission() {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,
        {
          title: "Microphone Permission",
          message: "This app needs access to your microphone.",
          buttonPositive: "OK",
          buttonNegative: "Cancel",
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        return true;
      } else {
        return false;
      }
    } catch (err) {
      console.warn(err);
    }
  }

  async function checkMicrophonePermission() {
    try {
      const result = await PermissionsAndroid.check(
        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO
      );
      if (result) {
        return true;
      } else {
        const result = await requestMicrophonePermission();
        return result;
      }
    } catch (err) {
      console.warn(err);
    }
  }

  // const detectSilence = (data: string) => {
  //   try {
  //     const byteArray = Buffer.from(data, "base64");
  //     const int16Array = new Int16Array(byteArray.buffer);

  //     const floatData = Array.from(int16Array).map((val) => val / 32768.0);
  //     const sumSquares = floatData.reduce((sum, val) => sum + val * val, 0);
  //     const rms = Math.sqrt(sumSquares / floatData.length);

  //     if (rms < silenceThreshold && !silenceTimer) {
  //       console.log("Starting silence timer");
  //       silenceTimer = setTimeout(() => {
  //         console.log("Silence duration reached, stopping recording");
  //         stopRecording();
  //       }, silenceDuration);
  //     } else if (rms >= silenceThreshold && silenceTimer) {
  //       console.log("Audio detected, resetting silence timer");
  //       clearTimeout(silenceTimer);
  //       silenceTimer = null;
  //     }
  //   } catch (error) {
  //     console.error("Error processing audio data:", error);
  //   }
  // };

  const detectSilence = (data: string) => {
    try {
      // Decode base64 audio data
      const binary = atob(data);
      const byteArray = new Uint8Array(binary.length);
      for (let i = 0; i < binary.length; i++) {
        byteArray[i] = binary.charCodeAt(i);
      }

      // Convert byteArray to Int16Array (PCM format)
      const int16Array = new Int16Array(byteArray.buffer);

      // Calculate RMS
      let sumSquares = 0;
      for (let i = 0; i < int16Array.length; i++) {
        const normalized = int16Array[i] / 32768.0;
        sumSquares += normalized * normalized;
      }
      const rms = Math.sqrt(sumSquares / int16Array.length);

      // Silence detection logic
      if (rms < silenceThreshold) {
        if (!silenceTimer) {
          console.log("Starting silence timer");
          silenceTimer = setTimeout(() => {
            console.log("Silence duration reached, stopping recording");
            stopRecording();
            silenceTimer = null; // Reset timer
          }, silenceDuration);
        }
      } else if (silenceTimer) {
        console.log("Audio detected, resetting silence timer");
        clearTimeout(silenceTimer);
        silenceTimer = null;
      }
    } catch (error) {
      console.error("Error processing audio data:", error);
    }
  };

  async function startRecording() {
    Speech.stop();
    setIsSpeaking(null);
    setRecording(true);
    setAudioData([]);

    // Reset silence timer if it exists
    if (silenceTimer) {
      clearTimeout(silenceTimer);
      silenceTimer = null;
    }

    try {
      // Set up audio data listener
      // const listener = AudioRecord.on("data", detectSilence);
      // Store the listener subscription
      // setAudioDataListener(listener);
      AudioRecord.start();
    } catch (error) {
      console.error("Error starting recording:", error);
    }
  }

  const loadWav = async () => {
    let buffer, result, byteArray;
    buffer = await RNFS.readFile(
      RNFS.DocumentDirectoryPath + "/test.wav",
      "base64"
    );
    byteArray = Buffer.from(buffer, "base64");
    result = wav.decode(byteArray);
    return await runModel(result.channelData[0]);
  };

  async function stopRecording() {
    try {
      setTranscribing(true);
      // Clear any existing silence timer

      // Remove the data listener
      if (audioDataListener) {
        audioDataListener.remove();
        setAudioDataListener(null);
      }
      setRecording(false);
      await AudioRecord.stop();
      await loadWav();
      setTranscribing(false);
      setAudioData([]);
      if (silenceTimer) {
        clearTimeout(silenceTimer);
        silenceTimer = null;
      }
    } catch (error) {
      setTranscribing(false);
      console.error("Error stopping recording:", error);
    }
  }

  async function initRecording() {
    const options = {
      sampleRate: 16000, // default 44100
      channels: 1, // 1 or 2, default 1
      bitsPerSample: 16, // 8 or 16, default 16
      audioSource: 6, // android only (see below)
      wavFile: "test.wav", // default 'audio.wav'
    };
    try {
      const result = await checkMicrophonePermission();
      if (result) {
        AudioRecord.init(options);
      }
    } catch (err) {
      console.error("Error AudioRecord.init", err);
    }
  }

  React.useEffect(() => {
    initRecording();

    // Cleanup function
    return () => {
      if (silenceTimer) {
        clearTimeout(silenceTimer);
      }
      if (audioDataListener) {
        audioDataListener.remove();
      }
    };
  }, []);

  return (
    <Pressable
      onPress={recording ? stopRecording : startRecording}
      style={{ height: 58, marginLeft: 10 }}
    >
      {transcribing ? (
        <ActivityIndicator size="large" color="#0000ff" />
      ) : (
        <FontAwesome
          name={recording ? "stop-circle" : "microphone"}
          size={36}
          color="#13538f"
        />
      )}
    </Pressable>
  );
}
