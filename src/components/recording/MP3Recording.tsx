
import * as React from "react";
import { Pressable, ActivityIndicator } from "react-native";
import { FontAwesome } from "@expo/vector-icons";
import { Audio } from "expo-av";
import { useEffect } from "react";
import {
  RecordingRefs,
  RecordingCallbacks,
  startRecording,
  stopRecording,
  cleanup,
  checkAndStartAutoRecording,
  AUDIO_LEVEL_CHECK_INTERVAL,
} from "../../utils/recordingHelper";

// Define an interface for the component props
interface MP3RecordingProps {
  Speech: { stop: () => void; isSpeakingAsync: () => Promise<boolean> }; // Speech can register onSpeechEnd
  setIsSpeaking: React.Dispatch<React.SetStateAction<any>>; // Type for the state setter
  onRecordingComplete: (uri: string) => void; // Callback function type
  autoStart?: boolean; // New prop to enable auto-start
  isSpeaking: boolean;
  transcriptionResult: any;
  onAudioLevelChange?: (level: number) => void; // Callback for audio level changes
}

const MP3Recording = React.forwardRef<any, MP3RecordingProps>(
  (
    {
      Speech,
      setIsSpeaking,
      onRecordingComplete,
      autoStart = false,
      isSpeaking,
      transcriptionResult,
      onAudioLevelChange,
    },
    ref
  ) => {
    const [isRecording, setIsRecording] = React.useState<boolean>(false);
    const [transcribing, setTranscribing] = React.useState<boolean>(false);
    
    // Refs for recording management
    const recordingRef = React.useRef<Audio.Recording | null>(null);
    const stopTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
    const silenceTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
    const silenceStopTimerRef = React.useRef<NodeJS.Timeout | null>(null);
    const lastAudioLevelRef = React.useRef<number>(0);
    const voiceActivityDetectedRef = React.useRef<boolean>(false);
    const hasStartedRef = React.useRef<boolean>(false);

    // Create refs object for helper functions
    const refs: RecordingRefs = {
      recordingRef,
      stopTimeoutRef,
      silenceTimeoutRef,
      silenceStopTimerRef,
      lastAudioLevelRef,
      voiceActivityDetectedRef,
      hasStartedRef,
    };

    // Create callbacks object for helper functions
    const callbacks: RecordingCallbacks = {
      setIsRecording,
      setTranscribing,
      onRecordingComplete,
      Speech,
      setIsSpeaking,
      onAudioLevelChange,
    };

    // Expose methods to parent component via ref
    React.useImperativeHandle(ref, () => ({
      restartRecording: () => {
        // Set hasStartedRef to true for consistency
        refs.hasStartedRef.current = true;
        if (!isRecording && !transcribing) {
          startRecording(refs, callbacks, autoStart);
        } else {
          console.log("Cannot restart - isRecording:", isRecording, "transcribing:", transcribing);
        }
      },
      stopRecording: () => {
        if (isRecording) {
          stopRecording(refs, callbacks, autoStart);
        }
      },
      isCurrentlyRecording: () => isRecording,
    }));

    // Auto-start recording when component mounts if autoStart is true
    useEffect(() => {
      const checkAndStart = async () => {
        await checkAndStartAutoRecording(
          refs,
          callbacks,
          autoStart,
          isRecording,
          isSpeaking,
          transcriptionResult
        );
      };
      
      setTimeout(() => {
        checkAndStart();
      }, 1000);
    }, [isSpeaking]);

    // Cleanup on unmount
    useEffect(() => {
      return () => {
        cleanup(refs).catch(error => {
          console.warn("Error during component cleanup:", error);
        });
      };
    }, []);

    return (
      <Pressable
        onPress={isRecording ? 
          () => stopRecording(refs, callbacks, autoStart) : 
          () => startRecording(refs, callbacks, autoStart)
        }
        style={{ height: 58, marginLeft: 10 }}
      >
        {transcribing ? (
          <ActivityIndicator size="large" color="#0000ff" />
        ) : (
          <FontAwesome
            name={isRecording ? "stop-circle" : "microphone"}
            size={36}
            color="#13538f"
          />
        )}
      </Pressable>
    );
  }
);

MP3Recording.displayName = "MP3Recording";

export default MP3Recording;
