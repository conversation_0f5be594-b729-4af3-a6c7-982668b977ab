import React from "react";
import { View, Text, StyleSheet } from "react-native";

interface AudioVisualizerProps {
  audioLevel: number; // Audio level in dB (-160 to 0)
  isRecording: boolean;
}

const AudioVisualizer: React.FC<AudioVisualizerProps> = ({ audioLevel, isRecording }) => {
  // Normalize audio level to a value between 0 and 1
  const normalizedLevel = Math.min(1, Math.max(0, (audioLevel + 160) / 160));
  
  // Calculate how many bars should be visible based on the audio level
  const barCount = 30;
  const activeBars = Math.floor(normalizedLevel * barCount);
  
  // Create an array of bar heights for a more wave-like effect
  const barHeights = Array.from({ length: barCount }, (_, i) => {
    // Create a wave pattern
    const wave = Math.sin(i * 0.5) * 0.5 + 0.5;
    // Combine with audio level
    return Math.max(5, wave * normalizedLevel * 100);
  });

  return (
    <View style={styles.container}>
      {isRecording && (
        <View style={styles.content}>
          <View style={styles.visualizer}>
            {Array.from({ length: barCount }).map((_, index) => (
              <View
                key={index}
                style={[
                  styles.bar,
                  {
                    height: `${barHeights[index]}%`,
                    backgroundColor: index < activeBars ? 
                      // Gradient from green to red based on intensity
                      index < barCount * 0.3 ? 
                        "#4CAF50" : // Green for low levels
                        index < barCount * 0.7 ? 
                          "#FFEB3B" : // Yellow for medium levels
                          "#F44336" : // Red for high levels
                      "#e0e0e0", // Gray for inactive bars
                  },
                ]}
              />
            ))}
          </View>
          {/* <Text style={styles.levelText}>{audioLevel.toFixed(1)} dB</Text> */}
          <Text style={styles.recordingText}>صحبت کن ...</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: "center",
    justifyContent: "center",
    marginVertical: 15,
    width: "100%",
  },
  content: {
    alignItems: "center",
    width: "100%",
  },
  visualizer: {
    flexDirection: "row",
    alignItems: "flex-end",
    height: 80,
    paddingHorizontal: 15,
    width: "100%",
    justifyContent: "center",
  },
  bar: {
    width: 6,
    marginHorizontal: 1,
    borderRadius: 3,
    minWidth: 4,
  },
  levelText: {
    marginTop: 10,
    fontSize: 16,
    fontWeight: "600",
    color: "#13538f",
    textAlign: "center",
  },
  recordingText: {
    marginTop: 10,
    fontSize: 16,
    fontWeight: "600",
    color: "#bcc0c4ff",
    textAlign: "center",
    fontFamily: "EstedadRegular",
  },
});

export default AudioVisualizer;