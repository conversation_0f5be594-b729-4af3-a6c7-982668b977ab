import 'react-native-get-random-values';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  setItem: jest.fn(),
  getItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}));

// Mock expo-speech
jest.mock('expo-speech', () => ({
  speak: jest.fn(),
  stop: jest.fn(),
  isSpeakingAsync: jest.fn(),
}));

// Mock expo-av
jest.mock('expo-av', () => ({
  Audio: {
    getPermissionsAsync: jest.fn().mockResolvedValue({ granted: true }),
    requestPermissionsAsync: jest.fn().mockResolvedValue({ granted: true }),
    setAudioModeAsync: jest.fn().mockResolvedValue(undefined),
    Recording: {
      createAsync: jest.fn().mockResolvedValue({
        recording: {
          stopAndUnloadAsync: jest.fn().mockResolvedValue(undefined),
          getURI: jest.fn().mockReturnValue('mock-uri'),
          getStatusAsync: jest.fn().mockResolvedValue({
            isRecording: true,
            metering: 0.5,
          }),
        },
      }),
    },
    RecordingOptionsPresets: {
      HIGH_QUALITY: {
        android: {},
        ios: {},
      },
    },
    AndroidOutputFormat: {
      MPEG_4: 'mpeg4',
    },
    AndroidAudioEncoder: {
      AAC: 'aac',
    },
    IOSOutputFormat: {
      MPEG4AAC: 'mpeg4aac',
    },
    IOSAudioQuality: {
      HIGH: 'high',
    },
  },
}));

// Mock react-native-fs
jest.mock('react-native-fs', () => ({
  writeFile: jest.fn(),
  readFile: jest.fn(),
  exists: jest.fn(),
  unlink: jest.fn(),
}));

// Mock expo-clipboard
jest.mock('expo-clipboard', () => ({
  setStringAsync: jest.fn(),
  getStringAsync: jest.fn(),
}));

// Mock SVG components
jest.mock('react-native-svg', () => ({
  Svg: 'Svg',
  Path: 'Path',
}));

// Mock navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
}));

global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
};